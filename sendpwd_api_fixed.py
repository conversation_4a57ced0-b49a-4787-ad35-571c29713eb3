import requests
import json
import time
import logging
import ssl
import urllib3
from requests.adapters import HTTPAdapter
from urllib3.util.ssl_ import create_urllib3_context

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# ===== 配置区域 =====
API_URL = "https://a.cneefix.com/api/terminalbox/sendboxpwd"
INPUT_FILE = "data.txt"
DELAY = 1  # 请求间隔(秒)
TIMEOUT = 30  # 超时时间
HEADERS = {
    "Content-Type": "application/json",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
}

# ===== 日志配置 =====
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('api_requests.log'),
        logging.StreamHandler()
    ]
)

# ===== 自定义SSL适配器 =====
class LegacySSLAdapter(HTTPAdapter):
    def init_poolmanager(self, *args, **kwargs):
        ctx = ssl.create_default_context()
        ctx.set_ciphers('DEFAULT@SECLEVEL=1')
        ctx.check_hostname = False
        ctx.verify_mode = ssl.CERT_NONE
        kwargs['ssl_context'] = ctx
        return super().init_poolmanager(*args, **kwargs)

# ===== 请求发送函数 =====
def send_request(json_data):
    """发送API请求"""
    
    # 方案1：使用自定义SSL适配器
    try:
        session = requests.Session()
        session.mount("https://", LegacySSLAdapter())
        
        response = session.post(
            API_URL,
            json=json_data,
            headers=HEADERS,
            timeout=TIMEOUT,
            verify=False
        )
        
        if response.status_code == 200:
            logging.info(f"请求成功: {response.status_code}")
            try:
                result = response.json()
                logging.info(f"响应内容: {result}")
            except:
                logging.info(f"响应文本: {response.text[:200]}")
            return True
        else:
            logging.warning(f"请求失败，状态码: {response.status_code}, 响应: {response.text[:200]}")
            return False
            
    except Exception as e:
        logging.warning(f"SSL适配器请求失败: {str(e)}")
    
    # 方案2：基础请求（禁用SSL验证）
    try:
        response = requests.post(
            API_URL,
            json=json_data,
            headers=HEADERS,
            timeout=TIMEOUT,
            verify=False
        )
        
        if response.status_code == 200:
            logging.info(f"基础请求成功: {response.status_code}")
            try:
                result = response.json()
                logging.info(f"响应内容: {result}")
            except:
                logging.info(f"响应文本: {response.text[:200]}")
            return True
        else:
            logging.warning(f"基础请求失败，状态码: {response.status_code}, 响应: {response.text[:200]}")
            return False
            
    except Exception as e:
        logging.error(f"所有请求方案均失败: {str(e)}")
        return False

# ===== 文件处理 =====
def process_file():
    """处理数据文件"""
    success = failure = 0
    
    try:
        with open(INPUT_FILE, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue

                try:
                    data = json.loads(line)
                    logging.info(f"处理第 {line_num} 行数据")
                    
                    if send_request(data):
                        success += 1
                        logging.info(f"第 {line_num} 行处理成功")
                    else:
                        failure += 1
                        logging.error(f"第 {line_num} 行处理失败")
                        
                except json.JSONDecodeError as e:
                    logging.error(f"第 {line_num} 行JSON解析错误: {e}")
                    logging.error(f"错误内容: {line[:100]}...")
                    failure += 1

                time.sleep(DELAY)
                
    except FileNotFoundError:
        logging.error(f"文件 {INPUT_FILE} 不存在")
        return
    except Exception as e:
        logging.error(f"文件处理错误: {e}")
        return

    logging.info(f"处理完成 - 成功: {success}, 失败: {failure}")

if __name__ == "__main__":
    print("=== API请求工具 ===")
    print(f"目标URL: {API_URL}")
    print(f"数据文件: {INPUT_FILE}")
    print("开始处理...")
    
    process_file()
    
    print("处理完成，详细日志请查看 api_requests.log 文件")
