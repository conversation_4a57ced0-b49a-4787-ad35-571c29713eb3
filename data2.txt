2025-06-17 15:22:15--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617152214","AppId":"wlg20220517","Sign":"MDA4ZDVjNTllMDA1NjRjNjc1ZTZjZjk2ZDBlMDg3YWM=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 15:22:15--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 15:22:15--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617152214","AppId":"33ff76586930302b","Sign":"MGRmMjIxNzBmZmFmY2RiOWFlMWFjZGU0YWU2OWNiMmM=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 15:22:17--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617152214","AppId":"wlg20220517","Sign":"MDA4ZDVjNTllMDA1NjRjNjc1ZTZjZjk2ZDBlMDg3YWM=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 15:22:17--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 15:22:17--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617152214","AppId":"33ff76586930302b","Sign":"MGRmMjIxNzBmZmFmY2RiOWFlMWFjZGU0YWU2OWNiMmM=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 15:22:19--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617101416","AppId":"wlg20220517","Sign":"MjUxZDk3MGY3MjAwMDNlZTY1ZDA5YmU0NzA1OGYzZTM=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 15:22:19--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 15:22:19--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617101416","AppId":"02859fca770a56d0","Sign":"N2I5MDNlZGYzNWM5Yzg3NDNlODVhMDBhMGUxNDk0ZmQ=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 15:26:17--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617152614","AppId":"wlg20220517","Sign":"NTU1NGJiMzAzZTliYWM5YWU0YTdhNDFmMGM5OWU3Mzk=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 15:26:18--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 15:26:18--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617152614","AppId":"33ff76586930302b","Sign":"YWE1MGY4YzQxNDc4YmNmMTI3MDI0ZDg1NjllNTkyZDk=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 15:26:20--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 15:26:21--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"11","BoxGroupNumber":"1","OrderNumber":"********010041750122309619458","PickCode":"728ceecdafa05ec1a553c91b50ca933b","MessageType":1,"StateType":0,"SignDate":"20250617152620","AppId":"wlg20220517","Sign":"MmJiN2VjZGQzZWEzZGYzZDIwZjVmOWE0MTM2NmRjZmE=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 15:26:22--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 15:26:22--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"11","BoxGroupNumber":"1","OrderNumber":"********010041750122309619458","PickCode":"78f779892dbb4a43fdb4e8685702d0dd","MessageType":1,"StateType":0,"SignDate":"20250617152620","AppId":"33ff76586930302b","Sign":"NDQ4NWI4Y2U3ZmRmZjFmNTNhZmUxNjk4NTBjMDJkNTE=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 15:26:23--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 15:26:24--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"26","BoxGroupNumber":"1","OrderNumber":"********010041750122276151890","PickCode":"8412e65fc96307ce052e1e4019661c34","MessageType":1,"StateType":0,"SignDate":"20250617152623","AppId":"wlg20220517","Sign":"N2M4MTdlNzU2OTJkMTM3NTMxYjc4MDk4ZGZhYWYxOWM=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 15:26:24--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 15:26:24--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"26","BoxGroupNumber":"1","OrderNumber":"********010041750122276151890","PickCode":"958570bbc166c54010858e55db173f24","MessageType":1,"StateType":0,"SignDate":"20250617152623","AppId":"33ff76586930302b","Sign":"ODMxMzUyNDU0YWFjODNkNzA1MzkzMzcyNWNjNWE5ZjI=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 15:26:25--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 15:26:34--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617101416","AppId":"wlg20220517","Sign":"MjUxZDk3MGY3MjAwMDNlZTY1ZDA5YmU0NzA1OGYzZTM=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 15:26:34--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 15:26:34--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617101416","AppId":"02859fca770a56d0","Sign":"N2I5MDNlZGYzNWM5Yzg3NDNlODVhMDBhMGUxNDk0ZmQ=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 15:26:43--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 15:29:24--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617101416","AppId":"wlg20220517","Sign":"MjUxZDk3MGY3MjAwMDNlZTY1ZDA5YmU0NzA1OGYzZTM=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 15:29:24--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 15:29:24--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617101416","AppId":"02859fca770a56d0","Sign":"N2I5MDNlZGYzNWM5Yzg3NDNlODVhMDBhMGUxNDk0ZmQ=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 15:29:25--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 15:58:06--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617091413","AppId":"wlg20220517","Sign":"MDE0MTE1N2ZjOGM4MmUxNjM1NjY2YzVkZmEwMGM0MWU=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 15:58:06--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 15:58:06--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617091413","AppId":"33ff76586930302b","Sign":"ZjViZGUxYmIxMjA4NjdiYTUwOWY5YWEwMzlhNjc0MzE=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 15:58:07--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 15:58:16--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617091413","AppId":"wlg20220517","Sign":"MDE0MTE1N2ZjOGM4MmUxNjM1NjY2YzVkZmEwMGM0MWU=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 15:58:16--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 15:58:16--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617091413","AppId":"33ff76586930302b","Sign":"ZjViZGUxYmIxMjA4NjdiYTUwOWY5YWEwMzlhNjc0MzE=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 15:58:16--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:34:04--->sendboxpwd， 数据：{"Code":"********01008","BoxDoorNumber":"3","BoxGroupNumber":"1","OrderNumber":"********010081750149086474174","PickCode":"8c1004568381218fbcbe3c9786faf687","MessageType":1,"StateType":0,"SignDate":"20250617163402","AppId":"wlg20220517","Sign":"ZTg5OGZjNjM3ZmUxYjY3NjI0MjFmMzkzYmNlMjZjMmQ=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:34:04--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:34:04--->sendboxpwd post data:{"Code":"********01008","BoxDoorNumber":"3","BoxGroupNumber":"1","OrderNumber":"********010081750149086474174","PickCode":"a6fed2f97a5447abaf78feda17b4bbaf","MessageType":1,"StateType":0,"SignDate":"20250617163402","AppId":"33ff76586930302b","Sign":"NTMyMTIyZTVkYTY4NmQ0OTZiZGNmMzE1NDIxYTc3YjE=","LoginName":"燕子矶中学快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:34:04--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:34:05--->sendboxpwd， 数据：{"Code":"********01008","BoxDoorNumber":"17","BoxGroupNumber":"1","OrderNumber":"********010081750149095412344","PickCode":"3e5e4f7128619da16d1369eb88e7b678","MessageType":1,"StateType":0,"SignDate":"20250617163404","AppId":"wlg20220517","Sign":"MmEzOThjZjBmMGQxZjIxOGE3MDE4ZTAwN2NjZWVhMmQ=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:34:05--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:34:05--->sendboxpwd post data:{"Code":"********01008","BoxDoorNumber":"17","BoxGroupNumber":"1","OrderNumber":"********010081750149095412344","PickCode":"7708735edfa3a1d2b3a6a4d78020dd54","MessageType":1,"StateType":0,"SignDate":"20250617163404","AppId":"33ff76586930302b","Sign":"NjExZDcwMDNjMGI2YThjMjgzNWIxNDFlOTBkNWIwMWI=","LoginName":"燕子矶中学快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:34:05--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:34:06--->sendboxpwd， 数据：{"Code":"********01008","BoxDoorNumber":"15","BoxGroupNumber":"2","OrderNumber":"********010081750149100536386","PickCode":"bcf020edd12dcd58f1b97526ac2310bf","MessageType":1,"StateType":0,"SignDate":"20250617163405","AppId":"wlg20220517","Sign":"NzhkYmJiNjgzZGJmMjY2ZDdmMWUzY2E1OTgxN2IyZjQ=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:34:06--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:34:06--->sendboxpwd post data:{"Code":"********01008","BoxDoorNumber":"15","BoxGroupNumber":"2","OrderNumber":"********010081750149100536386","PickCode":"87b5e5a96ed4868be9bda7d0f7733af9","MessageType":1,"StateType":0,"SignDate":"20250617163405","AppId":"33ff76586930302b","Sign":"NmE5MDJjMzFmMTMxZTUxYjM1ZTFhYzU3NTQzZTUxMjk=","LoginName":"燕子矶中学快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:34:06--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:37:33--->confirmreceipt， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":null,"MessageType":0,"StateType":0,"SignDate":"20250617163732","AppId":"wlg20220517","Sign":"NDEwNzMyYWJlNDEzZWM5OGM4OGZjNWUzM2ZhZDhmYTU=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:37:33--->confirmreceipt post url:https://mczxbq.cneefix.com/api/terminalbox2/confirmreceipt
--------------IP:127.0.0.1
--------------
2025-06-17 16:37:33--->confirmreceipt 发送数据{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":null,"MessageType":0,"StateType":0,"SignDate":"20250617163732","AppId":"02859fca770a56d0","Sign":"MzFhMzQyNjljODM3ODY2ZDA4NWU5NDliZmNiYjM3MjM=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:37:39--->confirmreceipt 接口返回{"flag":1,"msg":"确认收货成功","data":{"total":2,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:27--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617091413","AppId":"wlg20220517","Sign":"MDE0MTE1N2ZjOGM4MmUxNjM1NjY2YzVkZmEwMGM0MWU=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:27--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:27--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617091413","AppId":"33ff76586930302b","Sign":"ZjViZGUxYmIxMjA4NjdiYTUwOWY5YWEwMzlhNjc0MzE=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:27--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:29--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617091413","AppId":"wlg20220517","Sign":"MDE0MTE1N2ZjOGM4MmUxNjM1NjY2YzVkZmEwMGM0MWU=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:29--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:29--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617091413","AppId":"33ff76586930302b","Sign":"ZjViZGUxYmIxMjA4NjdiYTUwOWY5YWEwMzlhNjc0MzE=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:29--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:30--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617091813","AppId":"wlg20220517","Sign":"NWQ3YWM3YzNmZmE4NGFlOWY5YTVjMjQyY2RhM2VjYjE=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:30--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:30--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617091813","AppId":"33ff76586930302b","Sign":"YTFmYzJiMzc5MDA5OGNhNDVjOTkyMDQ5MDJhYjQwYTA=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:30--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:31--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617091813","AppId":"wlg20220517","Sign":"NWQ3YWM3YzNmZmE4NGFlOWY5YTVjMjQyY2RhM2VjYjE=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:31--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:31--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617091813","AppId":"33ff76586930302b","Sign":"YTFmYzJiMzc5MDA5OGNhNDVjOTkyMDQ5MDJhYjQwYTA=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:31--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:32--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617092213","AppId":"wlg20220517","Sign":"YmJlNWIwMWZjNThlNTU0NTA2NjNhMTc1ODNiZjdiYWU=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:33--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:33--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617092213","AppId":"33ff76586930302b","Sign":"NTI5NGFiM2Q5MDE2ZjBmNDRmNmRlMzYzMTM2ZTY0Y2I=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:33--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:34--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617092213","AppId":"wlg20220517","Sign":"YmJlNWIwMWZjNThlNTU0NTA2NjNhMTc1ODNiZjdiYWU=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:34--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:34--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617092213","AppId":"33ff76586930302b","Sign":"NTI5NGFiM2Q5MDE2ZjBmNDRmNmRlMzYzMTM2ZTY0Y2I=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:34--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:35--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617092614","AppId":"wlg20220517","Sign":"NjUzODYzMjYyZTcyMTkxZDZkMWRhNzhlZWQ2YWY4NzI=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:35--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:35--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617092614","AppId":"33ff76586930302b","Sign":"NWMxMTRjMGUxMjE0MTk3NTZlM2UyNDlkNDYzYjFjNWI=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:35--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:36--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617092614","AppId":"wlg20220517","Sign":"NjUzODYzMjYyZTcyMTkxZDZkMWRhNzhlZWQ2YWY4NzI=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:36--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:36--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617092614","AppId":"33ff76586930302b","Sign":"NWMxMTRjMGUxMjE0MTk3NTZlM2UyNDlkNDYzYjFjNWI=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:37--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:38--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617093013","AppId":"wlg20220517","Sign":"OWQxYjI2YTI4NTQ3NDdkMzBiOTcxZGQxNzFlZjQwOTQ=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:38--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:38--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617093013","AppId":"33ff76586930302b","Sign":"YWIwYTM0NGFlZWQwNzczMDlkNDBmMjhhMjUyYjgyYzU=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:38--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:39--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617093013","AppId":"wlg20220517","Sign":"OWQxYjI2YTI4NTQ3NDdkMzBiOTcxZGQxNzFlZjQwOTQ=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:39--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:39--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617093013","AppId":"33ff76586930302b","Sign":"YWIwYTM0NGFlZWQwNzczMDlkNDBmMjhhMjUyYjgyYzU=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:39--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:40--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617093413","AppId":"wlg20220517","Sign":"ZGVkYmM2NjRkNWExMWNjNjAxNjUzODdiOTI0MWI0N2Y=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:40--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:40--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617093413","AppId":"33ff76586930302b","Sign":"Njg1MTcwMDA5NTEyYTA4Y2IxYThhYTY5YjhjYjk1YzU=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:41--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:42--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617093413","AppId":"wlg20220517","Sign":"ZGVkYmM2NjRkNWExMWNjNjAxNjUzODdiOTI0MWI0N2Y=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:42--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:42--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617093413","AppId":"33ff76586930302b","Sign":"Njg1MTcwMDA5NTEyYTA4Y2IxYThhYTY5YjhjYjk1YzU=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:42--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:43--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617093813","AppId":"wlg20220517","Sign":"N2NkMTU3OTY0MGRmYjI1MzMyNzYzNjA1ODEzZWU4NDA=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:43--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:43--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617093813","AppId":"33ff76586930302b","Sign":"YzlmMzczNjllMmM2ZDljY2I4MDRhMTFiNTEwODFjZDY=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:43--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:44--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617093813","AppId":"wlg20220517","Sign":"N2NkMTU3OTY0MGRmYjI1MzMyNzYzNjA1ODEzZWU4NDA=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:44--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:44--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617093813","AppId":"33ff76586930302b","Sign":"YzlmMzczNjllMmM2ZDljY2I4MDRhMTFiNTEwODFjZDY=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:44--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:46--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617094213","AppId":"wlg20220517","Sign":"ZGM1ZWIxYzA3YWQwMzg4MTYxY2RmZjRkZGIxZDYxOWE=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:46--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:46--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617094213","AppId":"33ff76586930302b","Sign":"ZjVmZTZiZDNmOTkyMmQxMzY5YWM1YzkwZTI2MmI0ODk=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:46--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:47--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617094213","AppId":"wlg20220517","Sign":"ZGM1ZWIxYzA3YWQwMzg4MTYxY2RmZjRkZGIxZDYxOWE=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:47--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:47--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617094213","AppId":"33ff76586930302b","Sign":"ZjVmZTZiZDNmOTkyMmQxMzY5YWM1YzkwZTI2MmI0ODk=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:47--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:48--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617094613","AppId":"wlg20220517","Sign":"ZjZhODAzNGE0ZTIyNDQ0ZGExMzRlZDA3NWEwYWI5YWQ=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:48--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:48--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617094613","AppId":"33ff76586930302b","Sign":"OWNmZDBkMjM3NTY0M2RjYWZiZDY3NDBhNDY4MWIyMWY=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:49--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:50--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617094613","AppId":"wlg20220517","Sign":"ZjZhODAzNGE0ZTIyNDQ0ZGExMzRlZDA3NWEwYWI5YWQ=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:50--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:50--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617094613","AppId":"33ff76586930302b","Sign":"OWNmZDBkMjM3NTY0M2RjYWZiZDY3NDBhNDY4MWIyMWY=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:50--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:51--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617095013","AppId":"wlg20220517","Sign":"OGFkYTdjMTUxNzViYzBhMDI3OGQyMzhkNjc1Y2VmN2M=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:51--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:51--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617095013","AppId":"33ff76586930302b","Sign":"ZTQ5NTQ2N2RjZjhkMzgzNTk4MzRjNDRkMDQ4NjIwNGQ=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:51--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:52--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617095013","AppId":"wlg20220517","Sign":"OGFkYTdjMTUxNzViYzBhMDI3OGQyMzhkNjc1Y2VmN2M=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:52--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:52--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617095013","AppId":"33ff76586930302b","Sign":"ZTQ5NTQ2N2RjZjhkMzgzNTk4MzRjNDRkMDQ4NjIwNGQ=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:53--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:54--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617095413","AppId":"wlg20220517","Sign":"ODhmODUxN2M4Mzg4ZjA0MzE0NzAyODFmMDI1ZTZhYzg=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:54--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:54--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617095413","AppId":"33ff76586930302b","Sign":"Mjk3NWUxZDRlNDZhMmM3NDBkMTA2NDA0ZjJjOWUzODY=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:54--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:55--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617095413","AppId":"wlg20220517","Sign":"ODhmODUxN2M4Mzg4ZjA0MzE0NzAyODFmMDI1ZTZhYzg=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:55--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:55--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617095413","AppId":"33ff76586930302b","Sign":"Mjk3NWUxZDRlNDZhMmM3NDBkMTA2NDA0ZjJjOWUzODY=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:55--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:56--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617095814","AppId":"wlg20220517","Sign":"NmYwZjc5ZTBkNjI4MWJkNzhhNTkwMWI1MmNjOGQ1MDQ=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:56--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:56--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617095814","AppId":"33ff76586930302b","Sign":"YTNjNzA1OTU2ZjNkNjU1ZDk3YWRjMmQyNzc3NzQxYjA=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:57--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:58--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617095814","AppId":"wlg20220517","Sign":"NmYwZjc5ZTBkNjI4MWJkNzhhNTkwMWI1MmNjOGQ1MDQ=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:58--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:58--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617095814","AppId":"33ff76586930302b","Sign":"YTNjNzA1OTU2ZjNkNjU1ZDk3YWRjMmQyNzc3NzQxYjA=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:58--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:59--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617100213","AppId":"wlg20220517","Sign":"NzdmMDk0ZmQxYzllNzFlY2QyYWIxNjgzMThmNjJjMTA=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:59--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:59--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617100213","AppId":"33ff76586930302b","Sign":"MThlZTg1MjY3NDZjMDllZGE2OThlYTEwMjUyYjNjMDk=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:57:59--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:00--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617100213","AppId":"wlg20220517","Sign":"NzdmMDk0ZmQxYzllNzFlY2QyYWIxNjgzMThmNjJjMTA=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:00--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:00--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617100213","AppId":"33ff76586930302b","Sign":"MThlZTg1MjY3NDZjMDllZGE2OThlYTEwMjUyYjNjMDk=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:00--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:02--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617100613","AppId":"wlg20220517","Sign":"ZTFlMDJmZDg0YTZiZTQ4MTAyZjk2MTIwZTQ5MzRkNjI=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:02--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:02--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617100613","AppId":"33ff76586930302b","Sign":"MTA1MjQ2YzRjZTZiMDMxYTY1NDRmZjFmODQyN2Q3NjA=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:02--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:03--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617100613","AppId":"wlg20220517","Sign":"ZTFlMDJmZDg0YTZiZTQ4MTAyZjk2MTIwZTQ5MzRkNjI=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:03--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:03--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617100613","AppId":"33ff76586930302b","Sign":"MTA1MjQ2YzRjZTZiMDMxYTY1NDRmZjFmODQyN2Q3NjA=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:03--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:04--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617101013","AppId":"wlg20220517","Sign":"OTcxNDk3YzNlZjljNTNkZGI2ZjMyZjQ5YWFjNjVlNDE=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:04--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:04--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617101013","AppId":"33ff76586930302b","Sign":"MjI5MzQ2N2NlNWRlMjkwYjY2YTYxYzIwYzU0MzYxMWI=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:05--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:06--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617101013","AppId":"wlg20220517","Sign":"OTcxNDk3YzNlZjljNTNkZGI2ZjMyZjQ5YWFjNjVlNDE=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:06--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:06--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617101013","AppId":"33ff76586930302b","Sign":"MjI5MzQ2N2NlNWRlMjkwYjY2YTYxYzIwYzU0MzYxMWI=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:06--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:07--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617101413","AppId":"wlg20220517","Sign":"ODRkMTAwY2M0MDRjZjU4Y2JjYTc0Y2EzMTIxNGFjYzA=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:07--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:07--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617101413","AppId":"33ff76586930302b","Sign":"OTE4ZTk4NGJmMzc3MGI3MTQ4NjUzYzAyNWE4YTY1M2I=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:07--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:08--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617101413","AppId":"wlg20220517","Sign":"ODRkMTAwY2M0MDRjZjU4Y2JjYTc0Y2EzMTIxNGFjYzA=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:08--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:08--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617101413","AppId":"33ff76586930302b","Sign":"OTE4ZTk4NGJmMzc3MGI3MTQ4NjUzYzAyNWE4YTY1M2I=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:09--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:10--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617101416","AppId":"wlg20220517","Sign":"MjUxZDk3MGY3MjAwMDNlZTY1ZDA5YmU0NzA1OGYzZTM=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:10--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:10--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617101416","AppId":"02859fca770a56d0","Sign":"N2I5MDNlZGYzNWM5Yzg3NDNlODVhMDBhMGUxNDk0ZmQ=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:10--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:11--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617101416","AppId":"wlg20220517","Sign":"MjUxZDk3MGY3MjAwMDNlZTY1ZDA5YmU0NzA1OGYzZTM=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:11--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:11--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617101416","AppId":"02859fca770a56d0","Sign":"N2I5MDNlZGYzNWM5Yzg3NDNlODVhMDBhMGUxNDk0ZmQ=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:11--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:12--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617101813","AppId":"wlg20220517","Sign":"MWFlYjcxMmQ2YzhlMmVlYTBhMGEwMDQxOWI5OGVmOWY=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:12--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:12--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617101813","AppId":"33ff76586930302b","Sign":"ZWExMTFhNDRmNmM1MjkxYTM3ZjUxOGNmNmFlMzY0ZWI=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:12--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:13--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617101813","AppId":"wlg20220517","Sign":"MWFlYjcxMmQ2YzhlMmVlYTBhMGEwMDQxOWI5OGVmOWY=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:13--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:13--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617101813","AppId":"33ff76586930302b","Sign":"ZWExMTFhNDRmNmM1MjkxYTM3ZjUxOGNmNmFlMzY0ZWI=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:14--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:15--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617101817","AppId":"wlg20220517","Sign":"YmVlNGY2ZjNjZTk1ZWExM2MzOGI5MTRjNDQwNWY0ODk=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:15--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:15--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617101817","AppId":"02859fca770a56d0","Sign":"NWU5YzI1MGE5ODczN2NlMjdmYzhiNzdmMzEzZTdiYmY=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:15--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:16--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617101817","AppId":"wlg20220517","Sign":"YmVlNGY2ZjNjZTk1ZWExM2MzOGI5MTRjNDQwNWY0ODk=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:16--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:16--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617101817","AppId":"02859fca770a56d0","Sign":"NWU5YzI1MGE5ODczN2NlMjdmYzhiNzdmMzEzZTdiYmY=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:16--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:17--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617102213","AppId":"wlg20220517","Sign":"MTY1MGQ3Zjc3M2FjNmE2MDBkZjEwM2FlODU0MTFhZDE=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:17--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:17--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617102213","AppId":"33ff76586930302b","Sign":"MzNkMDQ3Mjc0ZjZhMTBhZTFhZDRlYTY3YzAyNjY4ZTc=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:17--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:18--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617102213","AppId":"wlg20220517","Sign":"MTY1MGQ3Zjc3M2FjNmE2MDBkZjEwM2FlODU0MTFhZDE=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:18--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:18--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617102213","AppId":"33ff76586930302b","Sign":"MzNkMDQ3Mjc0ZjZhMTBhZTFhZDRlYTY3YzAyNjY4ZTc=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:19--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:20--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617102217","AppId":"wlg20220517","Sign":"YzNhMDFhOTg5MjhkYmE1ZGRjY2YyNWI4Y2Q1ZjY5ZjQ=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:20--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:20--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617102217","AppId":"02859fca770a56d0","Sign":"NTQwMjRjOGM0YWY2ZjZkNGZlMTY0MWE5YjYzYTI4NDU=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:20--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:21--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617102217","AppId":"wlg20220517","Sign":"YzNhMDFhOTg5MjhkYmE1ZGRjY2YyNWI4Y2Q1ZjY5ZjQ=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:21--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:21--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617102217","AppId":"02859fca770a56d0","Sign":"NTQwMjRjOGM0YWY2ZjZkNGZlMTY0MWE5YjYzYTI4NDU=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:21--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:22--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617102613","AppId":"wlg20220517","Sign":"MmVhOGQ4YWYxNjQwNDdjYTUzMmQyYzM0NmJmZjZkZDY=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:22--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:22--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617102613","AppId":"33ff76586930302b","Sign":"NDk4YTI1ZDVjNzQ3NzU0ZmUyYjM2NWUyNTlmNWMwMWY=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:22--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:23--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617102613","AppId":"wlg20220517","Sign":"MmVhOGQ4YWYxNjQwNDdjYTUzMmQyYzM0NmJmZjZkZDY=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:23--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:23--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617102613","AppId":"33ff76586930302b","Sign":"NDk4YTI1ZDVjNzQ3NzU0ZmUyYjM2NWUyNTlmNWMwMWY=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:24--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:25--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617102617","AppId":"wlg20220517","Sign":"ZWU5NDU0NGQxYjI2MjlhODA5YTE2Njg2NzljOTNkOGY=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:25--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:25--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617102617","AppId":"02859fca770a56d0","Sign":"NTI1YzU4N2NiZjQ5MjAwZDg4MmU3ODgzMjBlYjkxMzU=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:25--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:26--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617102617","AppId":"wlg20220517","Sign":"ZWU5NDU0NGQxYjI2MjlhODA5YTE2Njg2NzljOTNkOGY=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:26--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:26--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617102617","AppId":"02859fca770a56d0","Sign":"NTI1YzU4N2NiZjQ5MjAwZDg4MmU3ODgzMjBlYjkxMzU=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:26--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:27--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617103013","AppId":"wlg20220517","Sign":"MDlmZDVlODk1NWZiMjcwOWZkMzRjNGE2YTAzYTkzMmE=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:27--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:27--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617103013","AppId":"33ff76586930302b","Sign":"ZWFlZjZkNmQ3NmY4ZDI3NTMzN2Y0ZWViZTFlNDQ4MTM=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:27--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:29--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617103013","AppId":"wlg20220517","Sign":"MDlmZDVlODk1NWZiMjcwOWZkMzRjNGE2YTAzYTkzMmE=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:29--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:29--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617103013","AppId":"33ff76586930302b","Sign":"ZWFlZjZkNmQ3NmY4ZDI3NTMzN2Y0ZWViZTFlNDQ4MTM=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:29--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:30--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617103017","AppId":"wlg20220517","Sign":"MjU2MmZkYWY1MGMwODFmODY4YjQ4Y2U3MGJjMmQzMzI=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:30--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:30--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617103017","AppId":"02859fca770a56d0","Sign":"Y2NjNmQwZDIxMGU5ZDcwZDY5ZjFmZWVlNDQ4MmIxZGQ=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:30--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:31--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617103017","AppId":"wlg20220517","Sign":"MjU2MmZkYWY1MGMwODFmODY4YjQ4Y2U3MGJjMmQzMzI=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:31--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:31--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617103017","AppId":"02859fca770a56d0","Sign":"Y2NjNmQwZDIxMGU5ZDcwZDY5ZjFmZWVlNDQ4MmIxZGQ=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:31--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:32--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617103414","AppId":"wlg20220517","Sign":"YjQ2Y2JiNjE3ZjA5ZThmM2IyNGQzNTFkMzk0ZjAxMDA=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:32--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:32--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617103414","AppId":"33ff76586930302b","Sign":"OTBhNTY5NTI2Y2ZhNzlmNjA4MTNkNTQyMmFiYWQ1Nzg=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:33--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:34--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617103414","AppId":"wlg20220517","Sign":"YjQ2Y2JiNjE3ZjA5ZThmM2IyNGQzNTFkMzk0ZjAxMDA=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:34--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:34--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617103414","AppId":"33ff76586930302b","Sign":"OTBhNTY5NTI2Y2ZhNzlmNjA4MTNkNTQyMmFiYWQ1Nzg=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:34--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:35--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617103417","AppId":"wlg20220517","Sign":"ZTVkZjBkZGMzOTMxZGU3ZGIzNzNkZjVkMWMxMzBhYTk=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:35--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:35--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617103417","AppId":"02859fca770a56d0","Sign":"YmQxMWUyYzgwYTE1ZWJjNDQzMGUxNjJiOTIzMDNlYTM=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:35--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:36--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617103417","AppId":"wlg20220517","Sign":"ZTVkZjBkZGMzOTMxZGU3ZGIzNzNkZjVkMWMxMzBhYTk=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:36--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:36--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617103417","AppId":"02859fca770a56d0","Sign":"YmQxMWUyYzgwYTE1ZWJjNDQzMGUxNjJiOTIzMDNlYTM=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:36--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:37--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617103814","AppId":"wlg20220517","Sign":"OWQ1NjAxNGIyNThlMDIxNWY3ZTU1OTBhZGIzOTNlMWY=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:37--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:37--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617103814","AppId":"33ff76586930302b","Sign":"MTk4YTgxY2UyZjVhMjEzYjE4M2NhMDk2ZTA4ZTE3OTg=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:38--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:39--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617103814","AppId":"wlg20220517","Sign":"OWQ1NjAxNGIyNThlMDIxNWY3ZTU1OTBhZGIzOTNlMWY=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:39--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:39--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617103814","AppId":"33ff76586930302b","Sign":"MTk4YTgxY2UyZjVhMjEzYjE4M2NhMDk2ZTA4ZTE3OTg=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:39--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:40--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617103817","AppId":"wlg20220517","Sign":"MWM4ZjhhNWZmYWY1ODdiYmNmNWMwOWNjY2Q0YzM3MjU=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:40--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:40--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617103817","AppId":"02859fca770a56d0","Sign":"ZjA5ZmEzNTZlNjJhNmFkYTUwYzUyOGY0OTZiODJlODA=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:40--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:41--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617103817","AppId":"wlg20220517","Sign":"MWM4ZjhhNWZmYWY1ODdiYmNmNWMwOWNjY2Q0YzM3MjU=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:41--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:41--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617103817","AppId":"02859fca770a56d0","Sign":"ZjA5ZmEzNTZlNjJhNmFkYTUwYzUyOGY0OTZiODJlODA=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:41--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:42--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617104213","AppId":"wlg20220517","Sign":"M2FiNDUyYjQ1NjBiODhmNGNiZmZhMzZiMjgzZWVjYjc=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:43--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:43--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617104213","AppId":"33ff76586930302b","Sign":"ZTYyN2FkZmM0Y2FmMTAwZjEzNmE4YTI4MGIyYzU1ZjY=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:43--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:44--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617104213","AppId":"wlg20220517","Sign":"M2FiNDUyYjQ1NjBiODhmNGNiZmZhMzZiMjgzZWVjYjc=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:44--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:44--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617104213","AppId":"33ff76586930302b","Sign":"ZTYyN2FkZmM0Y2FmMTAwZjEzNmE4YTI4MGIyYzU1ZjY=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:44--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:45--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617104217","AppId":"wlg20220517","Sign":"MWExYTQzMjA3ZTczMjNhMmJlMWI0MjRhNGRhYmRjNzg=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:45--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:45--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617104217","AppId":"02859fca770a56d0","Sign":"ZTA1ZjM3NTQ0NjQxMzc2ZDVlM2RmMzYxMTU2ZTQ3NDY=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:45--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:46--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617104217","AppId":"wlg20220517","Sign":"MWExYTQzMjA3ZTczMjNhMmJlMWI0MjRhNGRhYmRjNzg=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:46--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:46--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617104217","AppId":"02859fca770a56d0","Sign":"ZTA1ZjM3NTQ0NjQxMzc2ZDVlM2RmMzYxMTU2ZTQ3NDY=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:46--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:48--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617104614","AppId":"wlg20220517","Sign":"N2Q5ZjVkYjUxOTA5YjZkMjRmYThiYjEyZmM1YWFmNTM=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:48--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:48--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617104614","AppId":"33ff76586930302b","Sign":"NWQ2YWMzMTVmYWE5N2Y4OTc1ZmFhMjE2OTQ5NWUxMjM=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:48--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:49--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617104614","AppId":"wlg20220517","Sign":"N2Q5ZjVkYjUxOTA5YjZkMjRmYThiYjEyZmM1YWFmNTM=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:49--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:49--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617104614","AppId":"33ff76586930302b","Sign":"NWQ2YWMzMTVmYWE5N2Y4OTc1ZmFhMjE2OTQ5NWUxMjM=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:49--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:50--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617104617","AppId":"wlg20220517","Sign":"NDRmYzBlYmE4N2QxMjQ4YjI0ZGMwMWE2NzA2YzBjOGU=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:50--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:50--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617104617","AppId":"02859fca770a56d0","Sign":"Njg2NjgyNTg2Y2UyMmYzMWI4MWNiZjRlM2NhM2ZmYzg=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:50--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:51--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617104617","AppId":"wlg20220517","Sign":"NDRmYzBlYmE4N2QxMjQ4YjI0ZGMwMWE2NzA2YzBjOGU=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:51--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:51--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617104617","AppId":"02859fca770a56d0","Sign":"Njg2NjgyNTg2Y2UyMmYzMWI4MWNiZjRlM2NhM2ZmYzg=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:51--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:53--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617105014","AppId":"wlg20220517","Sign":"ZGFmYjI4ODZhNTQ2YjgyZDQyZGQwMjQ2NzEwMDE3YTk=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:53--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:53--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617105014","AppId":"33ff76586930302b","Sign":"MjkzZjg1ODZmODUwNzI2ODYzZTEzZDM0ZWViMjM3YWQ=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:53--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:54--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617105014","AppId":"wlg20220517","Sign":"ZGFmYjI4ODZhNTQ2YjgyZDQyZGQwMjQ2NzEwMDE3YTk=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:54--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:54--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617105014","AppId":"33ff76586930302b","Sign":"MjkzZjg1ODZmODUwNzI2ODYzZTEzZDM0ZWViMjM3YWQ=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:54--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:55--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617105017","AppId":"wlg20220517","Sign":"Y2ZhZDg4YzE4YThjZmI0YjFjYjZiMTM4NWJlMjRkYzQ=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:55--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:55--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617105017","AppId":"02859fca770a56d0","Sign":"NjM5ZjU0M2QxNGZiZWEzMjdhMGQ1M2U3ZGRjMmZlYjI=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:55--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:56--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617105017","AppId":"wlg20220517","Sign":"Y2ZhZDg4YzE4YThjZmI0YjFjYjZiMTM4NWJlMjRkYzQ=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:56--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:56--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617105017","AppId":"02859fca770a56d0","Sign":"NjM5ZjU0M2QxNGZiZWEzMjdhMGQ1M2U3ZGRjMmZlYjI=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:57--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:58--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617105413","AppId":"wlg20220517","Sign":"MzExM2VhNzQ2YTQ5ZjA4NjAzNzk1YzgxOTg4NjZmMDc=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:58--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:58--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617105413","AppId":"33ff76586930302b","Sign":"ODY0ZmNmM2M1ZDJiOTRlZDliZjg1MTNlMjU2NDkyMmU=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:58--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:59--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617105413","AppId":"wlg20220517","Sign":"MzExM2VhNzQ2YTQ5ZjA4NjAzNzk1YzgxOTg4NjZmMDc=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:59--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:59--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617105413","AppId":"33ff76586930302b","Sign":"ODY0ZmNmM2M1ZDJiOTRlZDliZjg1MTNlMjU2NDkyMmU=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:58:59--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:00--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617105417","AppId":"wlg20220517","Sign":"ODE5NjZlMDkyMGQ5OTFiNDA2OWQ0NDNhZTkwYWE5YmI=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:00--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:00--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617105417","AppId":"02859fca770a56d0","Sign":"MTQzYjNkNjlmNmViNzMyNjNiOTljMDJiODM4ZGYzMGE=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:00--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:01--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617105417","AppId":"wlg20220517","Sign":"ODE5NjZlMDkyMGQ5OTFiNDA2OWQ0NDNhZTkwYWE5YmI=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:01--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:01--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617105417","AppId":"02859fca770a56d0","Sign":"MTQzYjNkNjlmNmViNzMyNjNiOTljMDJiODM4ZGYzMGE=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:01--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:03--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617105814","AppId":"wlg20220517","Sign":"ZGI1YTJmOGVkMmU2ODJlMDMzM2EwOGQyNTY3Y2I2ZTE=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:03--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:03--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617105814","AppId":"33ff76586930302b","Sign":"MjFmNWQyMzk5MDY3MTgxZjE3YzAwNjA3YWUwN2IwMDI=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:03--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:04--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617105814","AppId":"wlg20220517","Sign":"ZGI1YTJmOGVkMmU2ODJlMDMzM2EwOGQyNTY3Y2I2ZTE=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:04--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:04--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617105814","AppId":"33ff76586930302b","Sign":"MjFmNWQyMzk5MDY3MTgxZjE3YzAwNjA3YWUwN2IwMDI=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:04--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:05--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617105817","AppId":"wlg20220517","Sign":"YmVlM2NhNzI2MWMzOTFiZjI0ZGNjMTVmMDBhZjk2MDg=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:05--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:05--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617105817","AppId":"02859fca770a56d0","Sign":"MTg0ZTc0Y2Y3NGExZGRjMTQ0MjQ2YjEyMmUyMDJiZjc=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:05--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:06--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617105817","AppId":"wlg20220517","Sign":"YmVlM2NhNzI2MWMzOTFiZjI0ZGNjMTVmMDBhZjk2MDg=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:06--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:06--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617105817","AppId":"02859fca770a56d0","Sign":"MTg0ZTc0Y2Y3NGExZGRjMTQ0MjQ2YjEyMmUyMDJiZjc=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:07--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:08--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617110214","AppId":"wlg20220517","Sign":"NzU3NmU1MDNjYjdhM2QwMThjNjQwMzg5NGUzMmE1Mjk=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:08--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:08--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617110214","AppId":"33ff76586930302b","Sign":"MWQyMjdmODg5NmZjZjg4Mzc3MmY4MTM0ODRmZWFkM2U=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:08--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:09--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617110214","AppId":"wlg20220517","Sign":"NzU3NmU1MDNjYjdhM2QwMThjNjQwMzg5NGUzMmE1Mjk=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:09--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:09--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617110214","AppId":"33ff76586930302b","Sign":"MWQyMjdmODg5NmZjZjg4Mzc3MmY4MTM0ODRmZWFkM2U=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:09--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:10--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617110217","AppId":"wlg20220517","Sign":"YTZjZjk5ZWI1Zjg4OTM4ZDZlNDRmOTNiOWIyYmQxYTk=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:10--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:10--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617110217","AppId":"02859fca770a56d0","Sign":"Yjk4NTZhNTdlNTNmZGViZmM4YWU4NjE0OWFmYTQ4ODM=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:10--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:11--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617110217","AppId":"wlg20220517","Sign":"YTZjZjk5ZWI1Zjg4OTM4ZDZlNDRmOTNiOWIyYmQxYTk=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:11--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:11--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617110217","AppId":"02859fca770a56d0","Sign":"Yjk4NTZhNTdlNTNmZGViZmM4YWU4NjE0OWFmYTQ4ODM=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:11--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:13--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617110614","AppId":"wlg20220517","Sign":"ODc1MTliZTU5OTBjNjE1YjYyNzI4YTBmZTA1YzAzMjk=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:13--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:13--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617110614","AppId":"33ff76586930302b","Sign":"MGE1NDgwMzExNzdmZDhlYzQzYzRiYjVkZWY4ZjM3NDU=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:13--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:14--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617110614","AppId":"wlg20220517","Sign":"ODc1MTliZTU5OTBjNjE1YjYyNzI4YTBmZTA1YzAzMjk=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:14--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:14--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617110614","AppId":"33ff76586930302b","Sign":"MGE1NDgwMzExNzdmZDhlYzQzYzRiYjVkZWY4ZjM3NDU=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:14--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:15--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617110617","AppId":"wlg20220517","Sign":"NWVhNDMzNWM2YTY2NGZmNWMxZmJjMjIwNmE2MDgzOTg=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:15--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:15--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617110617","AppId":"02859fca770a56d0","Sign":"ZGViYWMxNDUxOGFhNTI2NTdkMTg4NTI0Yjk0OTg1OGM=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:15--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:16--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617110617","AppId":"wlg20220517","Sign":"NWVhNDMzNWM2YTY2NGZmNWMxZmJjMjIwNmE2MDgzOTg=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:16--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:16--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617110617","AppId":"02859fca770a56d0","Sign":"ZGViYWMxNDUxOGFhNTI2NTdkMTg4NTI0Yjk0OTg1OGM=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:17--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:18--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617111013","AppId":"wlg20220517","Sign":"MzM2YzdjZmYxYTFmNDkyNGZkZjlkNzhjN2VhMGVlZmQ=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:18--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:18--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617111013","AppId":"33ff76586930302b","Sign":"OWM5OGIwNjJlMTA2YzI5MWY2ODBiZTFmZWJjMjk0Yzc=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:18--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:19--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617111013","AppId":"wlg20220517","Sign":"MzM2YzdjZmYxYTFmNDkyNGZkZjlkNzhjN2VhMGVlZmQ=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:19--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:19--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617111013","AppId":"33ff76586930302b","Sign":"OWM5OGIwNjJlMTA2YzI5MWY2ODBiZTFmZWJjMjk0Yzc=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:19--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:20--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617111017","AppId":"wlg20220517","Sign":"YjNjNTk1NGEwNTFhMDViOWJlNGZmMWExYWI5M2RhMTg=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:20--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:20--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617111017","AppId":"02859fca770a56d0","Sign":"NzFlYTgwNDM1Y2E3OTE3M2ZhZTM4NjcwZjI5MTViMzI=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:20--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:21--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617111017","AppId":"wlg20220517","Sign":"YjNjNTk1NGEwNTFhMDViOWJlNGZmMWExYWI5M2RhMTg=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:21--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:21--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617111017","AppId":"02859fca770a56d0","Sign":"NzFlYTgwNDM1Y2E3OTE3M2ZhZTM4NjcwZjI5MTViMzI=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:22--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:23--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617111414","AppId":"wlg20220517","Sign":"MDM1MjJhMzgxZTc0MTc5ODJmNGQ2NWMzNzgwNDEzOTQ=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:23--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:23--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617111414","AppId":"33ff76586930302b","Sign":"OTFlYjIwYzdkYTg2ODc5MmFkMDlmN2FjNzM5MTI2ZDk=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:23--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:24--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617111414","AppId":"wlg20220517","Sign":"MDM1MjJhMzgxZTc0MTc5ODJmNGQ2NWMzNzgwNDEzOTQ=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:24--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:24--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617111414","AppId":"33ff76586930302b","Sign":"OTFlYjIwYzdkYTg2ODc5MmFkMDlmN2FjNzM5MTI2ZDk=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:24--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:25--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617111417","AppId":"wlg20220517","Sign":"MDk0ZDFiNjViMWNhYjQ4YmI4MjljNzJiZTM5Yzg5MmM=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:25--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:25--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617111417","AppId":"02859fca770a56d0","Sign":"NDdkZjgxZTAxN2I5NWYwNGMwZWQ3MWZmYjdlMDUxZjM=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:25--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:27--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617111417","AppId":"wlg20220517","Sign":"MDk0ZDFiNjViMWNhYjQ4YmI4MjljNzJiZTM5Yzg5MmM=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:27--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:27--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617111417","AppId":"02859fca770a56d0","Sign":"NDdkZjgxZTAxN2I5NWYwNGMwZWQ3MWZmYjdlMDUxZjM=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:27--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:28--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617111814","AppId":"wlg20220517","Sign":"OTZmNmMzM2U4YTk3ZjVjZDVlYWM1ZDU5ZTlhY2YzZDg=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:28--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:28--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617111814","AppId":"33ff76586930302b","Sign":"OTZhMzViYWRiZjU2ZWRlY2IyNTVjMjQ3ZDc1YTllZjE=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:28--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:29--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617111814","AppId":"wlg20220517","Sign":"OTZmNmMzM2U4YTk3ZjVjZDVlYWM1ZDU5ZTlhY2YzZDg=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:29--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:29--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617111814","AppId":"33ff76586930302b","Sign":"OTZhMzViYWRiZjU2ZWRlY2IyNTVjMjQ3ZDc1YTllZjE=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:29--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:30--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617111817","AppId":"wlg20220517","Sign":"ZTY2NDA3OTg3MjVkNGE1YTgyY2VlNjA1ZWFjNTI1NTM=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:30--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:30--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617111817","AppId":"02859fca770a56d0","Sign":"NDgyNmE3MzE5YTdjNmIwMDkwOGQwMWQwNDA1YzkwMDQ=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:30--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:32--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617111817","AppId":"wlg20220517","Sign":"ZTY2NDA3OTg3MjVkNGE1YTgyY2VlNjA1ZWFjNTI1NTM=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:32--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:32--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617111817","AppId":"02859fca770a56d0","Sign":"NDgyNmE3MzE5YTdjNmIwMDkwOGQwMWQwNDA1YzkwMDQ=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:32--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:33--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617112214","AppId":"wlg20220517","Sign":"YzFkYjM0MWJiMTU3NDVmZTc4MmE4NmViZTVjMDBjNzE=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:33--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:33--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617112214","AppId":"33ff76586930302b","Sign":"MGRjYzU2ZjRlYzgwMmMwMDIwMGYyODVjNWQ0NDA1YmU=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:33--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:34--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617112214","AppId":"wlg20220517","Sign":"YzFkYjM0MWJiMTU3NDVmZTc4MmE4NmViZTVjMDBjNzE=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:34--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:34--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617112214","AppId":"33ff76586930302b","Sign":"MGRjYzU2ZjRlYzgwMmMwMDIwMGYyODVjNWQ0NDA1YmU=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:34--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:35--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617112217","AppId":"wlg20220517","Sign":"YTdhNjVhNmE5MjBiZThkOGM0MDQxZjM0NDgwMmQ2Zjk=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:35--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:35--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617112217","AppId":"02859fca770a56d0","Sign":"NmNlOTI5MGY5Zjk4ODM4MDk3NWI4ZDE3MDMxOTBiNWQ=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:35--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:37--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617112217","AppId":"wlg20220517","Sign":"YTdhNjVhNmE5MjBiZThkOGM0MDQxZjM0NDgwMmQ2Zjk=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:37--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:37--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617112217","AppId":"02859fca770a56d0","Sign":"NmNlOTI5MGY5Zjk4ODM4MDk3NWI4ZDE3MDMxOTBiNWQ=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:37--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:38--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617112614","AppId":"wlg20220517","Sign":"MWEyNWY1ZmY5YjFmOGZjYzc3ZGU0Yzc5Y2VkOGIyNjE=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:38--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:38--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617112614","AppId":"33ff76586930302b","Sign":"ODI4N2IwNDE2ZmEyNWRjOWFmYTNiMjhlODlkNzA3NGM=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:38--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:39--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617112614","AppId":"wlg20220517","Sign":"MWEyNWY1ZmY5YjFmOGZjYzc3ZGU0Yzc5Y2VkOGIyNjE=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:39--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:39--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617112614","AppId":"33ff76586930302b","Sign":"ODI4N2IwNDE2ZmEyNWRjOWFmYTNiMjhlODlkNzA3NGM=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:39--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:40--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617112617","AppId":"wlg20220517","Sign":"YzhiZTlmNTg1NTY1ODcxM2I2OWZkN2ZhZGNmNzhhZWQ=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:40--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:40--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617112617","AppId":"02859fca770a56d0","Sign":"NmI3ZmNmYWM4ZjE1YjM1NDVmNDRkYzVlNjBhNDhmZWI=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:40--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:42--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617112617","AppId":"wlg20220517","Sign":"YzhiZTlmNTg1NTY1ODcxM2I2OWZkN2ZhZGNmNzhhZWQ=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:42--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:42--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617112617","AppId":"02859fca770a56d0","Sign":"NmI3ZmNmYWM4ZjE1YjM1NDVmNDRkYzVlNjBhNDhmZWI=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:42--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:43--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617113014","AppId":"wlg20220517","Sign":"Njg1YTk2ZWIxM2VjZWUzOTE4ZDEyNTQyMTRlYTU4N2Y=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:43--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:43--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617113014","AppId":"33ff76586930302b","Sign":"MWE5MGY2OTZkZGRjNDJkMmI5ZDZmZTUwMjE1YjliMWU=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:43--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:44--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617113014","AppId":"wlg20220517","Sign":"Njg1YTk2ZWIxM2VjZWUzOTE4ZDEyNTQyMTRlYTU4N2Y=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:44--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:44--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617113014","AppId":"33ff76586930302b","Sign":"MWE5MGY2OTZkZGRjNDJkMmI5ZDZmZTUwMjE1YjliMWU=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:44--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:45--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617113017","AppId":"wlg20220517","Sign":"YzNmMGU0ZWEzZmQzZWM3YTcxYTE2NzE3MWE4MDZlNjU=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:46--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:46--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617113017","AppId":"02859fca770a56d0","Sign":"N2Q2NjA3NTE4ZjdiMmMzMDVmMTRlYjlkNDQwOTU5MDc=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:46--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:47--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617113017","AppId":"wlg20220517","Sign":"YzNmMGU0ZWEzZmQzZWM3YTcxYTE2NzE3MWE4MDZlNjU=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:47--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:47--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617113017","AppId":"02859fca770a56d0","Sign":"N2Q2NjA3NTE4ZjdiMmMzMDVmMTRlYjlkNDQwOTU5MDc=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:47--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:48--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617113414","AppId":"wlg20220517","Sign":"Mjc3N2VmNTYwYzY2YTdlOWM2ODIxZjQ4ZDZjNDk2ZjI=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:48--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:48--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617113414","AppId":"33ff76586930302b","Sign":"M2E5ODYyZGYwY2M0ZjA2NTYyY2E3ODAwZGY3MjExOTI=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:48--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:49--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617113414","AppId":"wlg20220517","Sign":"Mjc3N2VmNTYwYzY2YTdlOWM2ODIxZjQ4ZDZjNDk2ZjI=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:49--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:49--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617113414","AppId":"33ff76586930302b","Sign":"M2E5ODYyZGYwY2M0ZjA2NTYyY2E3ODAwZGY3MjExOTI=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:49--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:51--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617113417","AppId":"wlg20220517","Sign":"OGE1MTQzOTlhNjg5MTJiMzhiZDk4NmM1ZDRiZmI5MGI=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:51--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:51--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617113417","AppId":"02859fca770a56d0","Sign":"MjAyNzgyYzQ4ZmZmM2JhOTY4NTc3NmI4Y2IyODM1YjQ=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:51--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:52--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617113417","AppId":"wlg20220517","Sign":"OGE1MTQzOTlhNjg5MTJiMzhiZDk4NmM1ZDRiZmI5MGI=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:52--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:52--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617113417","AppId":"02859fca770a56d0","Sign":"MjAyNzgyYzQ4ZmZmM2JhOTY4NTc3NmI4Y2IyODM1YjQ=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:52--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:53--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617113814","AppId":"wlg20220517","Sign":"ODUzY2NkN2EwMjRiNTlmZTNmZjc5MjJjY2QwZjMxYTE=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:53--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:53--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617113814","AppId":"33ff76586930302b","Sign":"N2I5YmY3MDQ4Yzk4ZTU5NTY3MTMzMzdlYjA2OWNhM2I=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:53--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:54--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617113814","AppId":"wlg20220517","Sign":"ODUzY2NkN2EwMjRiNTlmZTNmZjc5MjJjY2QwZjMxYTE=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:54--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:54--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617113814","AppId":"33ff76586930302b","Sign":"N2I5YmY3MDQ4Yzk4ZTU5NTY3MTMzMzdlYjA2OWNhM2I=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:54--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:56--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617113817","AppId":"wlg20220517","Sign":"YTM5Yjk0MzRlNzI1NDZlNTRmZDA2NTdhMjZhNzYxZGY=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:56--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:56--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617113817","AppId":"02859fca770a56d0","Sign":"NWViZjA4NjgwNTM3N2UzODE0MjkwMzllZDBmYTBiNGQ=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:56--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:57--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617113817","AppId":"wlg20220517","Sign":"YTM5Yjk0MzRlNzI1NDZlNTRmZDA2NTdhMjZhNzYxZGY=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:57--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:57--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617113817","AppId":"02859fca770a56d0","Sign":"NWViZjA4NjgwNTM3N2UzODE0MjkwMzllZDBmYTBiNGQ=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:57--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:58--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617114214","AppId":"wlg20220517","Sign":"ZjdjN2MwOWE0ZDY0M2ZlNjEyM2VhNzY5MmZlMWM2Y2U=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:58--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:58--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617114214","AppId":"33ff76586930302b","Sign":"ZTg4NjQ3YmVmY2U1NTg4YjA5NjcxNjQzZTU2ZjFkYTE=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:58--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:59--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617114214","AppId":"wlg20220517","Sign":"ZjdjN2MwOWE0ZDY0M2ZlNjEyM2VhNzY5MmZlMWM2Y2U=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:59--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:59--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617114214","AppId":"33ff76586930302b","Sign":"ZTg4NjQ3YmVmY2U1NTg4YjA5NjcxNjQzZTU2ZjFkYTE=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 16:59:59--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:01--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617114217","AppId":"wlg20220517","Sign":"MzViOGZlMDNiMzBmYzNmMmM3NTY1ZGU3MTQyMzI0MDI=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:01--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:01--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617114217","AppId":"02859fca770a56d0","Sign":"MmQyNGMwMzE3MzI1ZWY3YTUxMjk0MzhkYmI0ZjgwNmE=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:01--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:02--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617114217","AppId":"wlg20220517","Sign":"MzViOGZlMDNiMzBmYzNmMmM3NTY1ZGU3MTQyMzI0MDI=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:02--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:02--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617114217","AppId":"02859fca770a56d0","Sign":"MmQyNGMwMzE3MzI1ZWY3YTUxMjk0MzhkYmI0ZjgwNmE=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:02--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:03--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617114614","AppId":"wlg20220517","Sign":"NTdkMDczNmZlYmU5ZDU2ODE4MTIxNmY3YjVmZDY2Yzk=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:03--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:03--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617114614","AppId":"33ff76586930302b","Sign":"MjMyNGNkMTg3MGRjNTM4ODE4MzczZTIxMzNhZDg0MDU=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:03--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:04--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617114614","AppId":"wlg20220517","Sign":"NTdkMDczNmZlYmU5ZDU2ODE4MTIxNmY3YjVmZDY2Yzk=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:04--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:04--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617114614","AppId":"33ff76586930302b","Sign":"MjMyNGNkMTg3MGRjNTM4ODE4MzczZTIxMzNhZDg0MDU=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:04--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:06--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617114617","AppId":"wlg20220517","Sign":"YzMwMGVmZGI2YzM0NGVmMzAwMzUwOWFjZTY2ODI3Mjk=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:06--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:06--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617114617","AppId":"02859fca770a56d0","Sign":"MTY5ZWMwMTVjMzAxM2VlNWY4NWNiMWZmMGQyNjU1ZTM=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:06--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:07--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617114617","AppId":"wlg20220517","Sign":"YzMwMGVmZGI2YzM0NGVmMzAwMzUwOWFjZTY2ODI3Mjk=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:07--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:07--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617114617","AppId":"02859fca770a56d0","Sign":"MTY5ZWMwMTVjMzAxM2VlNWY4NWNiMWZmMGQyNjU1ZTM=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:07--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:08--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617115014","AppId":"wlg20220517","Sign":"Njk2MmRlZGZjZjhhNzcxOWI1NGZmMjU4ZmZiNzBkYTI=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:08--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:08--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617115014","AppId":"33ff76586930302b","Sign":"MjcxNDRlZmRkYTE0MTgyZjY3N2Y1ZTRmMTI2MGQ1Zjc=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:08--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:09--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617115014","AppId":"wlg20220517","Sign":"Njk2MmRlZGZjZjhhNzcxOWI1NGZmMjU4ZmZiNzBkYTI=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:09--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:09--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617115014","AppId":"33ff76586930302b","Sign":"MjcxNDRlZmRkYTE0MTgyZjY3N2Y1ZTRmMTI2MGQ1Zjc=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:10--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:11--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617115017","AppId":"wlg20220517","Sign":"MDJmYTQ2NDFkYTg2MmFkMGMyOWYwYjk4OWFkYjcyMDA=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:11--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:11--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617115017","AppId":"02859fca770a56d0","Sign":"NzNlOWQ5ZWM3YWRkZTFjOWEzMGFkNzU0MzRlOWQ4NmM=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:11--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:12--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617115017","AppId":"wlg20220517","Sign":"MDJmYTQ2NDFkYTg2MmFkMGMyOWYwYjk4OWFkYjcyMDA=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:12--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:12--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617115017","AppId":"02859fca770a56d0","Sign":"NzNlOWQ5ZWM3YWRkZTFjOWEzMGFkNzU0MzRlOWQ4NmM=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:12--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:13--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617115414","AppId":"wlg20220517","Sign":"ZThmMWVmOWY0MDZkZjg0NTNhNDliYjJhMmE1MTIyM2Y=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:13--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:13--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617115414","AppId":"33ff76586930302b","Sign":"ZTM2NDI3MzFjMzBjNWI0NDY2NGEwZDY2MGFhNmUwZGI=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:13--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:14--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617115414","AppId":"wlg20220517","Sign":"ZThmMWVmOWY0MDZkZjg0NTNhNDliYjJhMmE1MTIyM2Y=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:14--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:14--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617115414","AppId":"33ff76586930302b","Sign":"ZTM2NDI3MzFjMzBjNWI0NDY2NGEwZDY2MGFhNmUwZGI=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:15--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:16--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617115417","AppId":"wlg20220517","Sign":"MjU1ZDFlZmFjMTE5ZWM2ODU0MDJkMjQxMWZhMDZlMjQ=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:16--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:16--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617115417","AppId":"02859fca770a56d0","Sign":"OGFmM2YxZWE0Y2IyOTI4NzkwNGJjODJjMjIyNzYxYWM=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:16--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:17--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617115417","AppId":"wlg20220517","Sign":"MjU1ZDFlZmFjMTE5ZWM2ODU0MDJkMjQxMWZhMDZlMjQ=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:17--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:17--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617115417","AppId":"02859fca770a56d0","Sign":"OGFmM2YxZWE0Y2IyOTI4NzkwNGJjODJjMjIyNzYxYWM=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:17--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:18--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617115814","AppId":"wlg20220517","Sign":"ZTA5OWRjNTM4MWQ3YjE0NzAyYzJkZDMxNTdiN2RiMjI=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:18--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:18--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617115814","AppId":"33ff76586930302b","Sign":"MjQ1Mzg1NjlkZTFhZTI1YzVmNGJmM2E2MmFhZjU4ODM=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:18--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:19--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617115814","AppId":"wlg20220517","Sign":"ZTA5OWRjNTM4MWQ3YjE0NzAyYzJkZDMxNTdiN2RiMjI=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:19--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:19--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617115814","AppId":"33ff76586930302b","Sign":"MjQ1Mzg1NjlkZTFhZTI1YzVmNGJmM2E2MmFhZjU4ODM=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:20--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:21--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617115817","AppId":"wlg20220517","Sign":"MzUyOGZhN2YzMDFkZDFlNjI1NzA5YTE5ZDYzNWI2YzI=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:21--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:21--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617115817","AppId":"02859fca770a56d0","Sign":"MThhNGM2ZmZkNmM1Y2Y0NzY5ODM5MTNhMWQzMjUyNWE=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:21--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:22--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617115817","AppId":"wlg20220517","Sign":"MzUyOGZhN2YzMDFkZDFlNjI1NzA5YTE5ZDYzNWI2YzI=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:22--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:22--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617115817","AppId":"02859fca770a56d0","Sign":"MThhNGM2ZmZkNmM1Y2Y0NzY5ODM5MTNhMWQzMjUyNWE=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:22--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:23--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617120214","AppId":"wlg20220517","Sign":"OWM3MjEzODZlMjE3Yzk2NWIyOWRiNzUxZDI1MmVjMjE=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:23--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:23--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617120214","AppId":"33ff76586930302b","Sign":"YjQyYjlmY2Y5NGM4YWNjYTQyMzYyZDU0MjdkYTI1Mjc=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:23--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:24--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617120214","AppId":"wlg20220517","Sign":"OWM3MjEzODZlMjE3Yzk2NWIyOWRiNzUxZDI1MmVjMjE=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:24--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:24--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617120214","AppId":"33ff76586930302b","Sign":"YjQyYjlmY2Y5NGM4YWNjYTQyMzYyZDU0MjdkYTI1Mjc=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:25--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:26--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617120217","AppId":"wlg20220517","Sign":"OTBmMDlhOWQyZjllM2YxOTg4ZGQxM2RkNDZlZTMyMTE=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:26--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:26--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617120217","AppId":"02859fca770a56d0","Sign":"MTMzM2YyMmI1ZmU0MmE2ZWM4ODJjNTJiNzIzYmVmMjE=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:26--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:27--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617120217","AppId":"wlg20220517","Sign":"OTBmMDlhOWQyZjllM2YxOTg4ZGQxM2RkNDZlZTMyMTE=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:27--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:27--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617120217","AppId":"02859fca770a56d0","Sign":"MTMzM2YyMmI1ZmU0MmE2ZWM4ODJjNTJiNzIzYmVmMjE=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:27--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:28--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617120614","AppId":"wlg20220517","Sign":"NjM0YTVkOTBhMGJlOWU5NDYzMDI4NWRkY2U2MzdhZjU=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:28--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:28--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617120614","AppId":"33ff76586930302b","Sign":"N2E5ZWUwMTE5ZDhlZjE2YjQ1MDVmZjNlNmVkYTNjZjk=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:28--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:29--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617120614","AppId":"wlg20220517","Sign":"NjM0YTVkOTBhMGJlOWU5NDYzMDI4NWRkY2U2MzdhZjU=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:29--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:29--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617120614","AppId":"33ff76586930302b","Sign":"N2E5ZWUwMTE5ZDhlZjE2YjQ1MDVmZjNlNmVkYTNjZjk=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:30--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:31--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617120617","AppId":"wlg20220517","Sign":"OGIyOThiZTg1ODg5OGQwYzJhYmYwZDgxMmViNjI2MmI=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:31--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:31--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617120617","AppId":"02859fca770a56d0","Sign":"ZjI4YTdlNmU4ZWViMjJhODZjNTg5Y2Q5NTc1YmNmZjU=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:31--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:32--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617120617","AppId":"wlg20220517","Sign":"OGIyOThiZTg1ODg5OGQwYzJhYmYwZDgxMmViNjI2MmI=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:32--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:32--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617120617","AppId":"02859fca770a56d0","Sign":"ZjI4YTdlNmU4ZWViMjJhODZjNTg5Y2Q5NTc1YmNmZjU=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:32--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:33--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617121014","AppId":"wlg20220517","Sign":"N2MzOTk3OTQ4MmJjOWUwZjBiZTU2Y2E3ZTNmNjMwZmI=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:33--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:33--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617121014","AppId":"33ff76586930302b","Sign":"YjI4ZGMwMDljMzViZDI0MjBmZTJlYmFjNDNjMDcxN2Y=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:33--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:34--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617121014","AppId":"wlg20220517","Sign":"N2MzOTk3OTQ4MmJjOWUwZjBiZTU2Y2E3ZTNmNjMwZmI=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:34--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:34--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617121014","AppId":"33ff76586930302b","Sign":"YjI4ZGMwMDljMzViZDI0MjBmZTJlYmFjNDNjMDcxN2Y=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:35--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:36--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617121017","AppId":"wlg20220517","Sign":"ZDhhODBhNzQ2YzEzNjM4MTE2ZGY3YzQ3MjVlOTRiZTM=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:36--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:36--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617121017","AppId":"02859fca770a56d0","Sign":"ODA0ZjJlYTNiM2Q5ZjUwM2UxNzkxY2FjMTkyYmNiMDY=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:36--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:37--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617121017","AppId":"wlg20220517","Sign":"ZDhhODBhNzQ2YzEzNjM4MTE2ZGY3YzQ3MjVlOTRiZTM=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:37--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:37--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617121017","AppId":"02859fca770a56d0","Sign":"ODA0ZjJlYTNiM2Q5ZjUwM2UxNzkxY2FjMTkyYmNiMDY=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:37--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:38--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617121414","AppId":"wlg20220517","Sign":"ZDllODQ5Yjk4NzA2MWQyYzJjMWEzZTg2YzFjOWQ2MDY=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:38--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:38--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617121414","AppId":"33ff76586930302b","Sign":"ZmIyNzE0NDNkM2Y1MGY5MmM3M2ZiYWFhNmFmYTI5OWM=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:38--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:40--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617121414","AppId":"wlg20220517","Sign":"ZDllODQ5Yjk4NzA2MWQyYzJjMWEzZTg2YzFjOWQ2MDY=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:40--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:40--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617121414","AppId":"33ff76586930302b","Sign":"ZmIyNzE0NDNkM2Y1MGY5MmM3M2ZiYWFhNmFmYTI5OWM=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:40--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:41--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617121417","AppId":"wlg20220517","Sign":"Y2VhMmY3MDdkMGIyZTBjYmFiMzBjZmJiNzNjMmUyMTg=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:41--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:41--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617121417","AppId":"02859fca770a56d0","Sign":"MzFmODA3M2Q3N2ZjNTJjYTJlYWFlMzljZjQ2ODFmNDY=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:41--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:42--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617121417","AppId":"wlg20220517","Sign":"Y2VhMmY3MDdkMGIyZTBjYmFiMzBjZmJiNzNjMmUyMTg=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:42--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:42--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617121417","AppId":"02859fca770a56d0","Sign":"MzFmODA3M2Q3N2ZjNTJjYTJlYWFlMzljZjQ2ODFmNDY=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:42--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:43--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617121814","AppId":"wlg20220517","Sign":"ZDBlZjU5MTRjYWM5ZDUyMmFmZDgxNjExOGY4OTM0Mzk=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:43--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:43--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617121814","AppId":"33ff76586930302b","Sign":"OTUzYjE4YjY0NmNiYzJiMWYxYWU1M2NjZmU3MmU1Njg=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:44--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:45--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617121814","AppId":"wlg20220517","Sign":"ZDBlZjU5MTRjYWM5ZDUyMmFmZDgxNjExOGY4OTM0Mzk=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:45--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:45--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617121814","AppId":"33ff76586930302b","Sign":"OTUzYjE4YjY0NmNiYzJiMWYxYWU1M2NjZmU3MmU1Njg=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:45--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:46--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617121817","AppId":"wlg20220517","Sign":"NTdlMzJkNmIzNDQ3NGRiMGE1NzM0MTcyZDA5NzlmZGE=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:46--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:46--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617121817","AppId":"02859fca770a56d0","Sign":"Njc3MTZiNDM5NjcyNGU4ZDJkOWNlZDA1YWYyZjkwMWI=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:46--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:47--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617121817","AppId":"wlg20220517","Sign":"NTdlMzJkNmIzNDQ3NGRiMGE1NzM0MTcyZDA5NzlmZGE=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:47--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:47--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617121817","AppId":"02859fca770a56d0","Sign":"Njc3MTZiNDM5NjcyNGU4ZDJkOWNlZDA1YWYyZjkwMWI=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:47--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:49--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617122214","AppId":"wlg20220517","Sign":"ZWFkYTBmYTk4NjViMDcyZTA0MzE0YmIxYWUyOGNkNjk=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:49--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:49--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617122214","AppId":"33ff76586930302b","Sign":"NzBkZjY5NjAxOTA2YTNkNjE5Y2MzMDZmNmMxOThkNjA=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:49--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:50--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617122214","AppId":"wlg20220517","Sign":"ZWFkYTBmYTk4NjViMDcyZTA0MzE0YmIxYWUyOGNkNjk=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:50--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:50--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617122214","AppId":"33ff76586930302b","Sign":"NzBkZjY5NjAxOTA2YTNkNjE5Y2MzMDZmNmMxOThkNjA=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:50--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:51--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617122217","AppId":"wlg20220517","Sign":"MDQ1MzJkNzMxYTZkMTdkNmE3MmRlZWFjMTEyMzQ4ZTA=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:51--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:51--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617122217","AppId":"02859fca770a56d0","Sign":"NmRmMGM2ODEyYzE5ZjliNWM1NTYxOTg3YmE0NTgxNjI=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:51--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:52--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617122217","AppId":"wlg20220517","Sign":"MDQ1MzJkNzMxYTZkMTdkNmE3MmRlZWFjMTEyMzQ4ZTA=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:52--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:52--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617122217","AppId":"02859fca770a56d0","Sign":"NmRmMGM2ODEyYzE5ZjliNWM1NTYxOTg3YmE0NTgxNjI=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:52--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:53--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617122614","AppId":"wlg20220517","Sign":"YWFhNTcyMjJmNjZkMzk3N2E0NGJmYjgzMmI3M2MyMTg=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:54--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:54--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617122614","AppId":"33ff76586930302b","Sign":"NTA4MWNiZDAxOWQwYjMzYjI2NDcwYjUxMWM0NjIzODU=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:54--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:55--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617122614","AppId":"wlg20220517","Sign":"YWFhNTcyMjJmNjZkMzk3N2E0NGJmYjgzMmI3M2MyMTg=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:55--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:55--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617122614","AppId":"33ff76586930302b","Sign":"NTA4MWNiZDAxOWQwYjMzYjI2NDcwYjUxMWM0NjIzODU=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:55--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:56--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617122617","AppId":"wlg20220517","Sign":"NDEzZmE2M2QyMzA3ZDNiZjlhNjExMDQ3ZWQ5OWUxOTc=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:56--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:56--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617122617","AppId":"02859fca770a56d0","Sign":"MzFiNGFiMTcwZDJjYzM4YjhmZTdkMmYzMjI5YTZhYWE=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:56--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:57--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617122617","AppId":"wlg20220517","Sign":"NDEzZmE2M2QyMzA3ZDNiZjlhNjExMDQ3ZWQ5OWUxOTc=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:57--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:57--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617122617","AppId":"02859fca770a56d0","Sign":"MzFiNGFiMTcwZDJjYzM4YjhmZTdkMmYzMjI5YTZhYWE=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:57--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:59--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617123014","AppId":"wlg20220517","Sign":"YTRkNmExY2RhY2Y2MmRjZmE3ODczYzZjNGExNTYxOTY=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:59--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:59--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617123014","AppId":"33ff76586930302b","Sign":"OTQwNjRhZDA0YTI5YTQzYWQzMWI0MDI2ZGJlMDk1YjE=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:00:59--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:00--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617123014","AppId":"wlg20220517","Sign":"YTRkNmExY2RhY2Y2MmRjZmE3ODczYzZjNGExNTYxOTY=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:00--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:00--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617123014","AppId":"33ff76586930302b","Sign":"OTQwNjRhZDA0YTI5YTQzYWQzMWI0MDI2ZGJlMDk1YjE=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:00--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:01--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617123017","AppId":"wlg20220517","Sign":"ZjNmOTk0YmEwYzFmZTIwZTY2MTQzZGVmOTllMzNmN2Y=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:01--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:01--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617123017","AppId":"02859fca770a56d0","Sign":"NmE4NWNjNGRlMDRmYzlkMjNlODU5NjhlM2EwNWJmMTA=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:01--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:02--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617123017","AppId":"wlg20220517","Sign":"ZjNmOTk0YmEwYzFmZTIwZTY2MTQzZGVmOTllMzNmN2Y=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:02--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:02--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617123017","AppId":"02859fca770a56d0","Sign":"NmE4NWNjNGRlMDRmYzlkMjNlODU5NjhlM2EwNWJmMTA=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:02--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:03--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617123414","AppId":"wlg20220517","Sign":"NzdhNzZkZjM3NjZjMjgyNjNjODczMjM2NzZmNjQzN2M=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:04--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:04--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617123414","AppId":"33ff76586930302b","Sign":"YTdiZWU3NWU1ZjllZTAwOGFkZTg2YmYyZjZkYTc2NzI=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:04--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:05--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617123414","AppId":"wlg20220517","Sign":"NzdhNzZkZjM3NjZjMjgyNjNjODczMjM2NzZmNjQzN2M=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:05--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:05--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617123414","AppId":"33ff76586930302b","Sign":"YTdiZWU3NWU1ZjllZTAwOGFkZTg2YmYyZjZkYTc2NzI=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:05--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:06--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617123417","AppId":"wlg20220517","Sign":"YTJhYmIwZjI0YWEyNDg0OTExN2M1ZTU4YzAyZTYxNTI=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:06--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:06--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617123417","AppId":"02859fca770a56d0","Sign":"ZjI2OWRhZTYxNWEzZDg5ZDU4YWMwYzI3ZjQ1MjBlMWI=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:06--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:07--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617123417","AppId":"wlg20220517","Sign":"YTJhYmIwZjI0YWEyNDg0OTExN2M1ZTU4YzAyZTYxNTI=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:07--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:07--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617123417","AppId":"02859fca770a56d0","Sign":"ZjI2OWRhZTYxNWEzZDg5ZDU4YWMwYzI3ZjQ1MjBlMWI=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:07--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:09--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617123814","AppId":"wlg20220517","Sign":"MDM4NTQ5YzBkNDA0NjVhMzE4NmQ4ZjEwMDhiODg4MzY=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:09--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:09--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617123814","AppId":"33ff76586930302b","Sign":"MWUzM2Y2MWQ2Y2Q2MTljMzM2MjYzOWYyYjk4YzUxNDk=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:09--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:10--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617123814","AppId":"wlg20220517","Sign":"MDM4NTQ5YzBkNDA0NjVhMzE4NmQ4ZjEwMDhiODg4MzY=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:10--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:10--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617123814","AppId":"33ff76586930302b","Sign":"MWUzM2Y2MWQ2Y2Q2MTljMzM2MjYzOWYyYjk4YzUxNDk=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:10--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:11--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617123817","AppId":"wlg20220517","Sign":"ZTE3YjhmM2FhYjQ2YjdhM2NhNzlhNzNiOTViMDA5NTY=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:11--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:11--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617123817","AppId":"02859fca770a56d0","Sign":"YTgwYzM1YzBkODhlYTk2OTE0MGY4NTg4MTE2MWUwOWY=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:11--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:12--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617123817","AppId":"wlg20220517","Sign":"ZTE3YjhmM2FhYjQ2YjdhM2NhNzlhNzNiOTViMDA5NTY=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:12--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:12--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617123817","AppId":"02859fca770a56d0","Sign":"YTgwYzM1YzBkODhlYTk2OTE0MGY4NTg4MTE2MWUwOWY=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:12--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:14--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617124214","AppId":"wlg20220517","Sign":"MTk5YTUxYzdlOWRjYTE3YmE0ZmQyNThjMzJiMjdjNDA=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:14--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:14--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617124214","AppId":"33ff76586930302b","Sign":"OTA1NDBlY2ZlYWRjZjY1MmVhMDA1NGU1MWU2Y2MwZTk=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:14--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:15--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617124214","AppId":"wlg20220517","Sign":"MTk5YTUxYzdlOWRjYTE3YmE0ZmQyNThjMzJiMjdjNDA=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:15--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:15--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617124214","AppId":"33ff76586930302b","Sign":"OTA1NDBlY2ZlYWRjZjY1MmVhMDA1NGU1MWU2Y2MwZTk=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:15--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:16--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617124217","AppId":"wlg20220517","Sign":"MmY1OTU2OTNiYzgwZTNkNjU3NzI0M2I4YTY3MzYwODk=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:16--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:16--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617124217","AppId":"02859fca770a56d0","Sign":"ZjMwNWQzNzJhNTQzYjk3OGIwODNhMTI1NzAyYmFkMzU=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:16--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:17--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617124217","AppId":"wlg20220517","Sign":"MmY1OTU2OTNiYzgwZTNkNjU3NzI0M2I4YTY3MzYwODk=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:17--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:17--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617124217","AppId":"02859fca770a56d0","Sign":"ZjMwNWQzNzJhNTQzYjk3OGIwODNhMTI1NzAyYmFkMzU=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:18--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:19--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617124614","AppId":"wlg20220517","Sign":"YzYwNWU0MGRlNjgzMmQwM2Y0MmJjZDE1Y2MyOGRjZTc=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:19--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:19--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617124614","AppId":"33ff76586930302b","Sign":"NDQ1OTE3MzljYjU0MDBjZjJjMDg5YjI4YjE4MzNjNzU=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:19--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:20--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617124614","AppId":"wlg20220517","Sign":"YzYwNWU0MGRlNjgzMmQwM2Y0MmJjZDE1Y2MyOGRjZTc=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:20--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:20--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617124614","AppId":"33ff76586930302b","Sign":"NDQ1OTE3MzljYjU0MDBjZjJjMDg5YjI4YjE4MzNjNzU=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:20--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:21--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617124617","AppId":"wlg20220517","Sign":"MmVlM2U2NWQ4MGVjNDE0ZGYzYTVkMDlkOGQxNjk2ZjU=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:21--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:21--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617124617","AppId":"02859fca770a56d0","Sign":"MzIwZjE2OWJkZDk5NTI2YjY5YTBlMDdiNDQzMzc2NWY=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:21--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:23--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617124617","AppId":"wlg20220517","Sign":"MmVlM2U2NWQ4MGVjNDE0ZGYzYTVkMDlkOGQxNjk2ZjU=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:23--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:23--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617124617","AppId":"02859fca770a56d0","Sign":"MzIwZjE2OWJkZDk5NTI2YjY5YTBlMDdiNDQzMzc2NWY=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:23--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:24--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617125014","AppId":"wlg20220517","Sign":"ZmUwNDhlYTE4NzYxYTExZDc3ZjRlM2ZhYjBhZTJjYmE=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:24--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:24--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617125014","AppId":"33ff76586930302b","Sign":"YjM3MzE1YWMzZGI5ZmJhZjVhNDU4N2JiZDI5YzVmZTc=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:24--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:25--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617125014","AppId":"wlg20220517","Sign":"ZmUwNDhlYTE4NzYxYTExZDc3ZjRlM2ZhYjBhZTJjYmE=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:25--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:25--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617125014","AppId":"33ff76586930302b","Sign":"YjM3MzE1YWMzZGI5ZmJhZjVhNDU4N2JiZDI5YzVmZTc=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:25--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:26--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617125017","AppId":"wlg20220517","Sign":"NzYzMzk1MDM5ODQ2MDAwNGU2Yjk3MDE4N2VjMzZjMWM=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:26--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:26--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617125017","AppId":"02859fca770a56d0","Sign":"MTYzMDNhODJlNDIzMDJmOGI0MjJhMWMzNTUyOGU4ODY=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:26--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:28--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617125017","AppId":"wlg20220517","Sign":"NzYzMzk1MDM5ODQ2MDAwNGU2Yjk3MDE4N2VjMzZjMWM=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:28--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:28--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617125017","AppId":"02859fca770a56d0","Sign":"MTYzMDNhODJlNDIzMDJmOGI0MjJhMWMzNTUyOGU4ODY=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:28--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:32--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617125414","AppId":"wlg20220517","Sign":"ZDFhNWJmYzE4N2U0MTQxNzkyZTZmYjNlZTFiNzYzMWQ=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:32--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:32--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617125414","AppId":"33ff76586930302b","Sign":"MDgyYmVjZDFiOGYyZmFhMDc2ZmY3YzZhZmJkYjA2NTI=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:32--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:33--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617125414","AppId":"wlg20220517","Sign":"ZDFhNWJmYzE4N2U0MTQxNzkyZTZmYjNlZTFiNzYzMWQ=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:33--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:33--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617125414","AppId":"33ff76586930302b","Sign":"MDgyYmVjZDFiOGYyZmFhMDc2ZmY3YzZhZmJkYjA2NTI=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:33--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:34--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617125417","AppId":"wlg20220517","Sign":"OTY1ODlhMGY1YmQ2MDI4ZWYxYzkwMGZlOGMwNzBmODg=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:34--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:34--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617125417","AppId":"02859fca770a56d0","Sign":"MTRhMGMxY2NkOGE4ZGI2M2RjZTk3ZjhiNzhmMDk2YjA=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:34--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:36--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617125417","AppId":"wlg20220517","Sign":"OTY1ODlhMGY1YmQ2MDI4ZWYxYzkwMGZlOGMwNzBmODg=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:36--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:36--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617125417","AppId":"02859fca770a56d0","Sign":"MTRhMGMxY2NkOGE4ZGI2M2RjZTk3ZjhiNzhmMDk2YjA=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:36--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:37--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617125814","AppId":"wlg20220517","Sign":"ODNmNDQ1OWIyZDcyY2ZjN2U2N2ZmNDMwMGM2MTBlZjE=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:37--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:37--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617125814","AppId":"33ff76586930302b","Sign":"MzUxMDU3YmNjZTI4MzJjNjExM2M2Yzc1OGIyNzYxN2Y=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:37--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:38--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617125814","AppId":"wlg20220517","Sign":"ODNmNDQ1OWIyZDcyY2ZjN2U2N2ZmNDMwMGM2MTBlZjE=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:38--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:38--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617125814","AppId":"33ff76586930302b","Sign":"MzUxMDU3YmNjZTI4MzJjNjExM2M2Yzc1OGIyNzYxN2Y=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:38--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:39--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617125817","AppId":"wlg20220517","Sign":"MmFjMjJlYjI5MzQyZjNiODUzN2Y3ZmUzZWU1NjdiODM=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:39--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:39--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617125817","AppId":"02859fca770a56d0","Sign":"ZjBiNTY5YjJkZmE0ZjczN2U5MTZkNTIzMmZmMDcxODY=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:39--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:41--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617125817","AppId":"wlg20220517","Sign":"MmFjMjJlYjI5MzQyZjNiODUzN2Y3ZmUzZWU1NjdiODM=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:41--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:41--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617125817","AppId":"02859fca770a56d0","Sign":"ZjBiNTY5YjJkZmE0ZjczN2U5MTZkNTIzMmZmMDcxODY=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:41--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:42--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617130214","AppId":"wlg20220517","Sign":"MGM3YTNiZWEyNjk5ZDAzZjA3NjE4YjliZTI3OTA3MGY=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:42--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:42--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617130214","AppId":"33ff76586930302b","Sign":"NmVlYjllMGU1NDMwMjQyZjA4Mzk4ZWNjZDE3YmRiMjU=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:42--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:43--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617130214","AppId":"wlg20220517","Sign":"MGM3YTNiZWEyNjk5ZDAzZjA3NjE4YjliZTI3OTA3MGY=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:43--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:43--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617130214","AppId":"33ff76586930302b","Sign":"NmVlYjllMGU1NDMwMjQyZjA4Mzk4ZWNjZDE3YmRiMjU=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:43--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:44--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617130217","AppId":"wlg20220517","Sign":"ZTRiNWU3ZTZhOGE3YjFmMzg0OWMzMjU3N2Q4ZDc2Y2I=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:44--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:44--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617130217","AppId":"02859fca770a56d0","Sign":"NTIyM2MxYmRkMDNjZWIwY2U1ZGIyMjI0MWQxZTdiZTk=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:44--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:45--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617130217","AppId":"wlg20220517","Sign":"ZTRiNWU3ZTZhOGE3YjFmMzg0OWMzMjU3N2Q4ZDc2Y2I=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:46--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:46--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617130217","AppId":"02859fca770a56d0","Sign":"NTIyM2MxYmRkMDNjZWIwY2U1ZGIyMjI0MWQxZTdiZTk=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:46--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:47--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617130614","AppId":"wlg20220517","Sign":"OGY0NWM5MzYzYzdlNmQ0NzNhYTAxMmNmMWM1MzE2ZmE=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:47--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:47--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617130614","AppId":"33ff76586930302b","Sign":"NzdhZTViOWJhNmYxMTdiYTg2OTBhYzkzNTRhZWE0MjI=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:47--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:48--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617130614","AppId":"wlg20220517","Sign":"OGY0NWM5MzYzYzdlNmQ0NzNhYTAxMmNmMWM1MzE2ZmE=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:48--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:48--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617130614","AppId":"33ff76586930302b","Sign":"NzdhZTViOWJhNmYxMTdiYTg2OTBhYzkzNTRhZWE0MjI=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:48--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:49--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617130617","AppId":"wlg20220517","Sign":"ZGQ2ZDU1NmQxOTdlMGVmODRiMzY0NDNkMzVlMzNjZDI=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:49--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:49--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617130617","AppId":"02859fca770a56d0","Sign":"ZDZhOGQ5ZWRkZTYyNDQxZmVkN2NmYjdhODkwZjgxODE=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:49--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:51--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617130617","AppId":"wlg20220517","Sign":"ZGQ2ZDU1NmQxOTdlMGVmODRiMzY0NDNkMzVlMzNjZDI=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:51--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:51--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617130617","AppId":"02859fca770a56d0","Sign":"ZDZhOGQ5ZWRkZTYyNDQxZmVkN2NmYjdhODkwZjgxODE=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:51--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:52--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617131014","AppId":"wlg20220517","Sign":"NDcxOTI2NzYyM2MwYjQ0ZTQxNGM2ODlkMTJiNDE2MTQ=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:52--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:52--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617131014","AppId":"33ff76586930302b","Sign":"YmI2Nzg5NDgzYWQ2N2EzMTNhNzYxMTM5ZWYyOWEyZDY=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:52--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:53--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617131014","AppId":"wlg20220517","Sign":"NDcxOTI2NzYyM2MwYjQ0ZTQxNGM2ODlkMTJiNDE2MTQ=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:53--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:53--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617131014","AppId":"33ff76586930302b","Sign":"YmI2Nzg5NDgzYWQ2N2EzMTNhNzYxMTM5ZWYyOWEyZDY=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:53--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:54--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617131017","AppId":"wlg20220517","Sign":"Mjc4OWVjYmIxYzE5NTg5YTdiMmYxOWQ3MWM2MjI1Njg=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:54--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:54--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617131017","AppId":"02859fca770a56d0","Sign":"YjJhNjI4ODg2MzY2MjA2NTYxYTU5ZTYxYzdkY2VlMGE=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:54--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:56--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617131017","AppId":"wlg20220517","Sign":"Mjc4OWVjYmIxYzE5NTg5YTdiMmYxOWQ3MWM2MjI1Njg=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:56--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:56--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617131017","AppId":"02859fca770a56d0","Sign":"YjJhNjI4ODg2MzY2MjA2NTYxYTU5ZTYxYzdkY2VlMGE=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:56--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:57--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617131414","AppId":"wlg20220517","Sign":"ZWFmNjViNmNkYWNjYzVkOWQwZjA5OTJkYTUzNjQxYmU=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:57--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:57--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617131414","AppId":"33ff76586930302b","Sign":"MmNhY2QzOWJkMjJiMmYwOWFhNWMwYTA4M2U0MDRlN2Y=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:57--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:58--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617131414","AppId":"wlg20220517","Sign":"ZWFmNjViNmNkYWNjYzVkOWQwZjA5OTJkYTUzNjQxYmU=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:58--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:58--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617131414","AppId":"33ff76586930302b","Sign":"MmNhY2QzOWJkMjJiMmYwOWFhNWMwYTA4M2U0MDRlN2Y=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:58--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:59--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617131417","AppId":"wlg20220517","Sign":"YzMzYmI4NjM0YzYxZjA0MjFmZTJmNDI3YzNmZWY0MGQ=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:59--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:59--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617131417","AppId":"02859fca770a56d0","Sign":"ZDNiZjAwZTkxYTI2ZTExMjE2ZGIwZDc2NmM3Y2Y1M2U=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:01:59--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:01--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617131417","AppId":"wlg20220517","Sign":"YzMzYmI4NjM0YzYxZjA0MjFmZTJmNDI3YzNmZWY0MGQ=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:01--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:01--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617131417","AppId":"02859fca770a56d0","Sign":"ZDNiZjAwZTkxYTI2ZTExMjE2ZGIwZDc2NmM3Y2Y1M2U=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:01--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:02--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617131814","AppId":"wlg20220517","Sign":"OWU1MjE3OTdjYzFmM2Y4MDQ4M2ExNWVmM2U5ZThmMGE=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:02--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:02--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617131814","AppId":"33ff76586930302b","Sign":"ODM0ODQxMGU3ODhmM2Y1MDc5NmRkODkyNzg1Zjk0YWE=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:02--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:03--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617131814","AppId":"wlg20220517","Sign":"OWU1MjE3OTdjYzFmM2Y4MDQ4M2ExNWVmM2U5ZThmMGE=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:03--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:03--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617131814","AppId":"33ff76586930302b","Sign":"ODM0ODQxMGU3ODhmM2Y1MDc5NmRkODkyNzg1Zjk0YWE=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:03--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:04--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617131817","AppId":"wlg20220517","Sign":"NGYwZmI2NGI4OWU1NTA0ZmZjNmEzMDcyNDdlNTE1MTE=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:04--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:04--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617131817","AppId":"02859fca770a56d0","Sign":"ZGIwZDlkYzNiNWU4Y2RiNjRkYTIzOGRiYzk0YzU5M2E=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:04--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:06--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617131817","AppId":"wlg20220517","Sign":"NGYwZmI2NGI4OWU1NTA0ZmZjNmEzMDcyNDdlNTE1MTE=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:06--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:06--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617131817","AppId":"02859fca770a56d0","Sign":"ZGIwZDlkYzNiNWU4Y2RiNjRkYTIzOGRiYzk0YzU5M2E=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:06--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:07--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617132214","AppId":"wlg20220517","Sign":"YTg2NTYxMTY2MTA2MDM1MDMzZjFjODgwN2E1ZjA0ZGQ=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:07--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:07--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617132214","AppId":"33ff76586930302b","Sign":"OGVhOWRlYWVhZTdjMGE2NWI3ZWFmOWE5MmU5ZjA0OGE=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:07--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:08--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617132214","AppId":"wlg20220517","Sign":"YTg2NTYxMTY2MTA2MDM1MDMzZjFjODgwN2E1ZjA0ZGQ=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:08--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:08--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617132214","AppId":"33ff76586930302b","Sign":"OGVhOWRlYWVhZTdjMGE2NWI3ZWFmOWE5MmU5ZjA0OGE=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:08--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:09--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617132217","AppId":"wlg20220517","Sign":"MjZhMmFiMThlM2RiOTY0NDE4MmRjNzk0YjBiM2NiMTU=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:09--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:09--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617132217","AppId":"02859fca770a56d0","Sign":"MDc2YzM5YWU3ODI0NGYxY2QzOGVjZTRhYjQ5NjJlYTE=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:09--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:11--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617132217","AppId":"wlg20220517","Sign":"MjZhMmFiMThlM2RiOTY0NDE4MmRjNzk0YjBiM2NiMTU=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:11--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:11--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617132217","AppId":"02859fca770a56d0","Sign":"MDc2YzM5YWU3ODI0NGYxY2QzOGVjZTRhYjQ5NjJlYTE=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:11--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:12--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617132614","AppId":"wlg20220517","Sign":"YWI1OTIyMWYyOTJmMTFkZDFjMjQwYjFjNDA0M2IyNjM=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:12--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:12--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617132614","AppId":"33ff76586930302b","Sign":"ODk3Njg0Yzg4YjEyNTQxMGZhNTNhMGNhMTQzNjkyMzY=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:12--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:13--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617132614","AppId":"wlg20220517","Sign":"YWI1OTIyMWYyOTJmMTFkZDFjMjQwYjFjNDA0M2IyNjM=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:13--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:13--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617132614","AppId":"33ff76586930302b","Sign":"ODk3Njg0Yzg4YjEyNTQxMGZhNTNhMGNhMTQzNjkyMzY=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:13--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:14--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617132617","AppId":"wlg20220517","Sign":"ODdmNzRlOTEyY2Q5OWE2NzQyNTRkODI3N2I2YzY5ZTg=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:14--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:14--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617132617","AppId":"02859fca770a56d0","Sign":"YTU5M2M5YWE0MjZiZTNhNzQ5NGJmOGRkZjhmNTVlODg=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:14--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:16--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617132617","AppId":"wlg20220517","Sign":"ODdmNzRlOTEyY2Q5OWE2NzQyNTRkODI3N2I2YzY5ZTg=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:16--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:16--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617132617","AppId":"02859fca770a56d0","Sign":"YTU5M2M5YWE0MjZiZTNhNzQ5NGJmOGRkZjhmNTVlODg=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:16--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:17--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617133014","AppId":"wlg20220517","Sign":"ODJiOGZjMmI5ODJmZjhlOWU1OWMxMTkyNTJkYTMyODE=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:17--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:17--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617133014","AppId":"33ff76586930302b","Sign":"NTk4YzU3NGVlYWI2OTI1OTI2OWRmNmI2MzUxY2M1NDE=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:17--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:18--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617133014","AppId":"wlg20220517","Sign":"ODJiOGZjMmI5ODJmZjhlOWU1OWMxMTkyNTJkYTMyODE=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:18--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:18--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617133014","AppId":"33ff76586930302b","Sign":"NTk4YzU3NGVlYWI2OTI1OTI2OWRmNmI2MzUxY2M1NDE=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:18--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:19--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617133017","AppId":"wlg20220517","Sign":"ZWEyNWM3ODc1OWFkYjNlYWYxNjcxYzc4ZDcxNjhhMjA=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:19--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:19--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617133017","AppId":"02859fca770a56d0","Sign":"YTAwYzQxOTBiYmEwNjBkMzhkMjc1ZWNjMGNiNWY0OTU=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:19--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:21--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617133017","AppId":"wlg20220517","Sign":"ZWEyNWM3ODc1OWFkYjNlYWYxNjcxYzc4ZDcxNjhhMjA=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:21--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:21--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617133017","AppId":"02859fca770a56d0","Sign":"YTAwYzQxOTBiYmEwNjBkMzhkMjc1ZWNjMGNiNWY0OTU=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:21--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:22--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617133414","AppId":"wlg20220517","Sign":"MDg4NjFiZjUzYjQ5Y2I5ZDkwNDkwODExMDU3ZTM1MDU=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:22--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:22--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617133414","AppId":"33ff76586930302b","Sign":"YTUyMWM1ODNiZWYwMjk4ZjcwZDFhYzY0YTQ3ZDUzYjQ=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:22--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:23--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617133414","AppId":"wlg20220517","Sign":"MDg4NjFiZjUzYjQ5Y2I5ZDkwNDkwODExMDU3ZTM1MDU=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:23--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:23--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617133414","AppId":"33ff76586930302b","Sign":"YTUyMWM1ODNiZWYwMjk4ZjcwZDFhYzY0YTQ3ZDUzYjQ=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:23--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:24--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617133417","AppId":"wlg20220517","Sign":"MTU2MGQyMGQ1MTNiM2JmZTA2ZjRhMzEwZDhjZmY0ZWM=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:24--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:24--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617133417","AppId":"02859fca770a56d0","Sign":"NDk2YjcyZjY4NTg5ZWE0ZGNlNjc1NTIwN2VmYWNmNTg=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:24--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:26--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617133417","AppId":"wlg20220517","Sign":"MTU2MGQyMGQ1MTNiM2JmZTA2ZjRhMzEwZDhjZmY0ZWM=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:26--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:26--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617133417","AppId":"02859fca770a56d0","Sign":"NDk2YjcyZjY4NTg5ZWE0ZGNlNjc1NTIwN2VmYWNmNTg=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:26--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:27--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617133814","AppId":"wlg20220517","Sign":"NDdkNGNiY2I5YWIzMjQ4YzliMzYyNjhlYmM4MGE0N2Y=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:27--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:27--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617133814","AppId":"33ff76586930302b","Sign":"ODU2YTAxY2NlMjc0NmE0YWFhZWM5NGNkZThjNTI2ZmI=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:27--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:28--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617133814","AppId":"wlg20220517","Sign":"NDdkNGNiY2I5YWIzMjQ4YzliMzYyNjhlYmM4MGE0N2Y=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:28--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:28--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617133814","AppId":"33ff76586930302b","Sign":"ODU2YTAxY2NlMjc0NmE0YWFhZWM5NGNkZThjNTI2ZmI=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:28--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:29--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617133817","AppId":"wlg20220517","Sign":"MTE5YWExYjg0MTJkNzEyMGU1MmViMWYwYzkyZjEyNGM=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:29--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:29--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617133817","AppId":"02859fca770a56d0","Sign":"YzEwOGJiNzBkNjYwMzA5Mjc1MTQwMjljMWIwYjdlODM=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:29--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:31--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617133817","AppId":"wlg20220517","Sign":"MTE5YWExYjg0MTJkNzEyMGU1MmViMWYwYzkyZjEyNGM=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:31--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:31--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617133817","AppId":"02859fca770a56d0","Sign":"YzEwOGJiNzBkNjYwMzA5Mjc1MTQwMjljMWIwYjdlODM=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:31--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:32--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617134214","AppId":"wlg20220517","Sign":"YWUxNzE4MWY2YjIxOWIxN2U4ZmM2ZWU4ZjdlMjgwZWY=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:32--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:32--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617134214","AppId":"33ff76586930302b","Sign":"Njc3NmEyZjdmZDFiZDZhNDNjODRkNGEzMWMxMTlkZmI=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:32--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:33--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617134214","AppId":"wlg20220517","Sign":"YWUxNzE4MWY2YjIxOWIxN2U4ZmM2ZWU4ZjdlMjgwZWY=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:33--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:33--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617134214","AppId":"33ff76586930302b","Sign":"Njc3NmEyZjdmZDFiZDZhNDNjODRkNGEzMWMxMTlkZmI=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:33--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:34--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617134217","AppId":"wlg20220517","Sign":"OGJmODhlN2FhZjMzNjRhZTZjNzAyMmQ2NTlmZjE2NjQ=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:34--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:34--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617134217","AppId":"02859fca770a56d0","Sign":"MGU3MjFlNGJkMDI5ZGQ5N2E5MjJiN2EyNDc0MmMzODY=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:34--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:36--->sendboxpwd， 数据：{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"60acf4077546b8a213c88ac895a4b8ff","MessageType":1,"StateType":0,"SignDate":"20250617134217","AppId":"wlg20220517","Sign":"OGJmODhlN2FhZjMzNjRhZTZjNzAyMmQ2NTlmZjE2NjQ=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:36--->sendboxpwd post url:https://mczxbq.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:36--->sendboxpwd post data:{"Code":"A076051901001","BoxDoorNumber":"27","BoxGroupNumber":"1","OrderNumber":"A0760519010011750126188903710","PickCode":"2973267e568d601fe1a33aefc0f79124","MessageType":1,"StateType":0,"SignDate":"20250617134217","AppId":"02859fca770a56d0","Sign":"MGU3MjFlNGJkMDI5ZGQ5N2E5MjJiN2EyNDc0MmMzODY=","LoginName":"物流柜"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:36--->sendboxpwd 接口返回{"flag":0,"msg":"此物件已被取走","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:37--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617134614","AppId":"wlg20220517","Sign":"OTI5NGUwNzFhNjFmNmQ1YTFjMjkxNzE4YWJjZDVhNzM=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:37--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:37--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617134614","AppId":"33ff76586930302b","Sign":"NmVlZTQzYWQ3YmM1YTRjNjMxYWZkNDk4OWJiMzBiMDA=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:37--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:38--->sendboxpwd， 数据：{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"d5a75b954c1fbe9f2da82277e717cb0a","MessageType":1,"StateType":0,"SignDate":"20250617134614","AppId":"wlg20220517","Sign":"OTI5NGUwNzFhNjFmNmQ1YTFjMjkxNzE4YWJjZDVhNzM=","LoginName":null}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:38--->sendboxpwd post url:https://mjfcn.cneefix.com/api/terminalbox2/sendboxpwd
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:38--->sendboxpwd post data:{"Code":"********01004","BoxDoorNumber":"10","BoxGroupNumber":"1","OrderNumber":"********010041750122249323676","PickCode":"c401535ffb8562ebe85d52b1a2ed1793","MessageType":1,"StateType":0,"SignDate":"20250617134614","AppId":"33ff76586930302b","Sign":"NmVlZTQzYWQ3YmM1YTRjNjMxYWZkNDk4OWJiMzBiMDA=","LoginName":"科高快递柜内置账号"}
--------------IP:127.0.0.1
--------------
2025-06-17 17:02:38--->sendboxpwd 接口返回{"flag":1,"msg":"发送短信成功","data":{"total":1,"header":"[]","rows":"[]","footer":"[]","other":"[]","sessionId":""}}
--------------IP:127.0.0.1
--------------
