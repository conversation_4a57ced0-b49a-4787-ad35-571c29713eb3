import requests
import json
import time
import logging
from requests.adapters import HTTPAdapter
from urllib3.util.ssl_ import create_urllib3_context

# ===== 配置区域 =====
API_URL = "https://a.cneefix.com/api/terminalbox/sendboxpwd"
INPUT_FILE = "data.txt"
DELAY = 1  # 请求间隔(秒)
RETRY_COUNT = 3  # 增加重试次数
TIMEOUT = 20  # 延长超时时间
HEADERS = {"Content-Type": "application/json"}

# ===== 日志配置 =====
logging.basicConfig(
    level=logging.DEBUG,  # 改为DEBUG获取更多信息
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('api_debug.log'),
        logging.StreamHandler()
    ]
)

# ===== 方案1：现代SSL适配器 =====
class ModernSSLAdapter(HTTPAdapter):
    def init_poolmanager(self, *args, **kwargs):
        context = create_urllib3_context()
        context.minimum_version = ssl.TLSVersion.TLSv1_2
        kwargs['ssl_context'] = context
        return super().init_poolmanager(*args, **kwargs)

# ===== 方案2：curl_cffi (终极方案) =====
def try_curl_cffi(json_data):
    """使用curl_cffi模拟浏览器"""
    try:
        from curl_cffi import requests as curl_requests
        response = curl_requests.post(
            API_URL,
            json=json_data,
            headers=HEADERS,
            timeout=TIMEOUT,
            impersonate="chrome120"  # 模拟最新Chrome
        )
        return response.status_code == 200
    except ImportError:
        logging.warning("未安装curl_cffi，请运行: pip install curl_cffi")
        return False
    except Exception as e:
        logging.error(f"curl_cffi请求失败: {str(e)}")
        return False

# ===== 请求发送 =====
def send_request(json_data):
    """分阶段尝试不同方案"""
    # 阶段1：尝试requests+自定义SSL
    try:
        session = requests.Session()
        session.mount("https://", ModernSSLAdapter())
        response = session.post(
            API_URL,
            json=json_data,
            headers=HEADERS,
            timeout=TIMEOUT,
            verify=False  # 测试阶段禁用验证
        )
        response.raise_for_status()
        return True
    except Exception as e:
        logging.warning(f"标准请求失败，尝试备用方案: {str(e)}")

    # 阶段2：尝试curl_cffi
    if try_curl_cffi(json_data):
        return True

    # 阶段3：终极fallback（不验证证书）
    try:
        response = requests.post(
            API_URL,
            json=json_data,
            headers=HEADERS,
            timeout=TIMEOUT,
            verify=False
        )
        return response.status_code == 200
    except Exception as e:
        logging.error(f"所有方案均失败: {str(e)}")
        return False

# ===== 文件处理 =====
def process_file():
    success = failure = 0
    with open(INPUT_FILE, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue

            try:
                data = json.loads(line)
                if send_request(data):
                    success += 1
                else:
                    failure += 1
            except json.JSONDecodeError:
                logging.error(f"无效JSON: {line[:50]}...")
                failure += 1

            time.sleep(DELAY)

    logging.info(f"最终结果 - 成功: {success}, 失败: {failure}")

if __name__ == "__main__":
    print("=== 终极API请求工具 ===")
    process_file()